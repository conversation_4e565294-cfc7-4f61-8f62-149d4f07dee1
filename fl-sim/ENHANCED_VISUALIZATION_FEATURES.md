# Enhanced Visualization Features

This document describes the new enhanced visualization features added to the FL-SIM visualization module (`fl_sim/utils/viz.py`).

## Overview

The visualization module has been enhanced to support custom x-axis selection, allowing users to plot any metric from the JSON log files as the x-axis, with automatic sorting and proper aggregation for both training and validation data.

## Key Features

### 1. Custom X-Axis Selection
- **Previous behavior**: Only supported sequential indices (Global Iter.) as x-axis
- **New behavior**: Support any metric from the JSON log as x-axis (e.g., "step", "epoch", "comm", "time")
- **Automatic sorting**: Data points are automatically sorted by the selected x-axis metric

### 2. Enhanced Data Aggregation
- **Validation data**: Uses server data directly from the "val" section
- **Training data**: Aggregates client data (excluding server) using weighted averages based on `num_samples`
- **Flexible metrics**: Support for any y-axis metric (e.g., "acc", "loss", "top3_acc", etc.)

### 3. Updated Panel Interface
- **New UI components**: Added separate input fields for x-axis and y-axis metrics
- **Improved layout**: Better organization of metric selection controls
- **Enhanced caching**: Updated cache system to handle x/y metric combinations

## New Functions

### `aggregate_results_from_json_log_with_custom_axis()`

```python
def aggregate_results_from_json_log_with_custom_axis(
    d: Union[dict, str, Path], 
    part: str = "val", 
    x_metric: str = "step", 
    y_metric: str = "acc"
) -> Dict[str, np.ndarray]:
```

**Purpose**: Aggregate federated learning results with custom x and y axis metrics.

**Parameters**:
- `d`: JSON log data (dict) or path to JSON file
- `part`: Data part ("val" or "train")
- `x_metric`: Metric to use for x-axis (e.g., "step", "epoch", "comm")
- `y_metric`: Metric to use for y-axis (e.g., "acc", "loss", "top3_acc")

**Returns**: Dictionary with 'x' and 'y' numpy arrays

**Behavior**:
- For `part="val"`: Uses server data directly from the validation section
- For `part="train"`: Aggregates client data (excluding server) using weighted averages

### Updated `get_curves_and_labels_from_log()`

```python
def get_curves_and_labels_from_log(
    files: Union[str, Path, Sequence[Union[str, Path]]],
    part: str = "val",
    x_metric: str = "step",
    y_metric: str = "acc",
) -> Tuple[List[Dict[str, np.ndarray]], List[str]]:
```

**Changes**:
- Added `x_metric` parameter for x-axis metric selection
- Renamed `metric` parameter to `y_metric` for clarity
- Returns curves as dictionaries with 'x' and 'y' keys instead of just y-values

### Updated `plot_curves()`

```python
def plot_curves(
    files: Union[str, Path, Sequence[Union[str, Path]]],
    part: str = "val",
    x_metric: str = "step",
    y_metric: str = "acc",
    fig_ax: Optional[Tuple[plt.Figure, plt.Axes]] = None,
    labels: Union[str, Sequence[str]] = None,
) -> Tuple[plt.Figure, plt.Axes]:
```

**Changes**:
- Added `x_metric` parameter
- Renamed `metric` to `y_metric`
- Automatic x-axis labeling based on selected metric

## Usage Examples

### Example 1: Plot Communication Round vs Accuracy

```python
from fl_sim.utils.viz import plot_curves

# Plot validation accuracy vs communication rounds
fig, ax = plot_curves(
    files=["experiment_log.json"],
    part="val",
    x_metric="comm",  # Use communication round as x-axis
    y_metric="acc"    # Use accuracy as y-axis
)
```

### Example 2: Plot Training Loss vs Epochs

```python
# Plot aggregated training loss vs epochs
fig, ax = plot_curves(
    files=["experiment_log.json"],
    part="train",
    x_metric="epoch",  # Use epoch as x-axis
    y_metric="loss"    # Use loss as y-axis
)
```

### Example 3: Direct Data Aggregation

```python
from fl_sim.utils.viz import aggregate_results_from_json_log_with_custom_axis

# Load and aggregate data
result = aggregate_results_from_json_log_with_custom_axis(
    "experiment_log.json",
    part="train",
    x_metric="comm",
    y_metric="acc"
)

print(f"X values (comm rounds): {result['x']}")
print(f"Y values (accuracy): {result['y']}")
```

## Panel Interface Updates

### New UI Components

1. **X-axis Metric Input**: 
   - Field label: "X-axis metric"
   - Default value: "step"
   - Placeholder: "step/epoch/comm/..."

2. **Y-axis Metric Input**:
   - Field label: "Y-axis metric" 
   - Default value: "acc"
   - Placeholder: "acc/loss/..."

3. **Updated Refresh Button**:
   - New label: "Refresh metrics/ylabel"
   - Tooltip: "Refresh part and metrics"

### Layout Changes

The data selection horizontal box now includes:
```
[Part] [X-axis metric] [Y-axis metric] [Y label] [Refresh button]
```

## JSON Log File Structure

The enhanced functionality works with JSON log files having this structure:

```json
{
    "val": {
        "Server": [
            {
                "step": 0,
                "epoch": 0,
                "comm": 0,
                "acc": 0.132,
                "loss": 2.303,
                "num_samples": 1924
            }
        ]
    },
    "train": {
        "Client1": [
            {
                "step": 1,
                "epoch": 1,
                "comm": 1,
                "acc": 0.381,
                "loss": 2.166,
                "num_samples": 21
            }
        ],
        "Client2": [...],
        "Server": [...]
    }
}
```

## Backward Compatibility

- All existing functionality remains unchanged
- Legacy plotting functions still work with default parameters
- Existing Panel interface continues to work with enhanced features

## Technical Implementation Details

### Data Structure Changes

- **Legacy format**: Curves were numpy arrays representing y-values
- **New format**: Curves are dictionaries with 'x' and 'y' keys containing numpy arrays
- **Compatibility**: All plotting functions handle both formats automatically

### Aggregation Logic

For training data aggregation:
1. Collect all unique x-metric values across clients (excluding server)
2. For each x-value, compute weighted average of y-metric values
3. Weight by `num_samples` for proper federated averaging
4. Return sorted x and corresponding y arrays

### Caching Updates

Cache keys now include both x and y metrics:
```python
cache_key = f"{part}-{x_metric}-{y_metric}-{filename_stem}"
```

This ensures proper cache invalidation when switching between different metric combinations.
