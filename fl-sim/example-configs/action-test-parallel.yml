strategy:
  matrix:
    algorithm:
    # - Ditto
    # - FedDR
    - FedAvg
    - FedAdam
    # - FedProx
    # - FedPD
    # - FedSplit
    # - IFCA
    # - pFedMac
    # - pFedMe
    # - ProxSkip
    # - SCAFFOLD
    seed:
    - 0
    - 1
    - 2
    - 3
  parallel:
    mode: parallel_task # parallel mode: serial | parallel_task (default: serial)
    num_workers: 4 # Number of worker processes (default: CPU core count)

algorithm:
  name: ${{ matrix.algorithm }}
  server:
    num_clients: null
    clients_sample_ratio: 0.1
    num_iters: 2  # only for a quick test in GitHub Actions, normally much larger
    P: 0.3  # for FedPD, ProxSkip
    lr: 0.03  # for SCAFFOLD
    num_clusters: 10  # for IFCA
    log_dir: action-test
    tag: seed_${{ matrix.seed }}
  client:
    lr: 0.03
    num_epochs: 5
    batch_size: null  # null for default batch size
    scheduler:
      name: step  # StepLR
      step_size: 1
      gamma: 0.99
dataset:
  name: FedEMNIST
  datadir: null  # default dir
  transform: none  # none for static transform (only normalization, no augmentation)
model:
  name: cnn_femmist_tiny
seed: ${{ matrix.seed }}
