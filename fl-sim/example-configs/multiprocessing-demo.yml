# Example config file for demonstrating multiprocessing output management

strategy:
  matrix:
    algorithm:
    - FedAvg
    - FedProx
    clients_sample_ratio:
    - 0.3
    - 0.7
  parallel:
    mode: parallel_task
    num_workers: 2

mode: federated

seed: 42

dataset:
  name: FedProxFEMNIST
  data_dir: null  # will use default
  transform: null  # will use default
  target_transform: null  # will use default
  seed: null  # will use global seed
  num_clients: null  # will use default
  split_type: null  # will use default
  iid: false
  balance: null  # will use default
  partition: null  # will use default
  alpha: null  # will use default
  min_size: null  # will use default

model:
  name: cnn_femmist_tiny

algorithm:
  name: ${{ matrix.algorithm }}
  server:
    num_iters: 5
    num_clients: null  # will use default
    clients_sample_ratio: ${{ matrix.clients_sample_ratio }}
    lr: 0.01
    seed: null  # will use global seed
  client:
    num_epochs: 2
    batch_size: null  # will use default
