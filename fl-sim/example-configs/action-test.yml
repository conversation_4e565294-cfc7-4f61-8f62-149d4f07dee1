# This is a simple config file for testing fl-sim command line interface in GitHub Actions.

strategy:
  matrix:
    algorithm:
    # - Ditto
    # - FedDR
    - FedAvg
    - FedAdam
    # - FedProx
    # - FedPD
    # - FedSplit
    # - IFCA
    # - pFedMac
    # - pFedMe
    # - ProxSkip
    # - SCAFFOLD
    clients_sample_ratio:
    - 0.1
    - 0.3
    # - 0.7
    # - 1.0
  n_parallel: 1  # number of parallel jobs, not supported yet but kept for future use

algorithm:
  name: ${{ matrix.algorithm }}
  server:
    num_clients: null
    clients_sample_ratio: ${{ matrix.clients_sample_ratio }}
    num_iters: 2  # only for a quick test in GitHub Actions, normally much larger
    P: 0.3  # for FedPD, ProxSkip
    lr: 0.03  # for SCAFFOLD
    num_clusters: 10  # for IFCA
    log_dir: action-test
    tag: sample_ratio_${{ matrix.clients_sample_ratio }}-seed_${{ seed }}
  client:
    lr: 0.03
    num_epochs: 10
    batch_size: null  # null for default batch size
    scheduler:
      name: step  # StepLR
      step_size: 1
      gamma: 0.99
dataset:
  name: FedProxFEMNIST
  datadir: null  # default dir
  transform: none  # none for static transform (only normalization, no augmentation)
model:
  name: cnn_femmist_tiny
seed: 0
