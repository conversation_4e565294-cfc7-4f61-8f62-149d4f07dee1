# This is a simple config file for testing fl-sim command line interface in GitHub Actions
# without "strategy" to run multiple experiments.

algorithm:
  name: FedProx
  server:
    num_clients: null
    clients_sample_ratio: 0.1
    num_iters: 2  # only for a quick test in GitHub Actions, normally much larger
    P: 0.3  # for FedPD, ProxSkip
    lr: 0.03  # for SCAFFOLD
    num_clusters: 10  # for IFCA
    log_dir: action-test
    tag: sample_ratio_${{ algorithm.server.clients_sample_ratio }}-seed_${{ seed }}
  client:
    lr: 0.03
    beta: 0.05
    gamma: 1.2
    prox: 20.0
    num_epochs: 10
    batch_size: null  # null for default batch size
    scheduler:
      name: step  # StepLR
      step_size: 1
      gamma: 0.99
dataset:
  name: FedProxFEMNIST
  datadir: null  # default dir
  transform: none  # none for static transform (only normalization, no augmentation)
model:
  name: cnn_femmist_tiny
seed: 0
