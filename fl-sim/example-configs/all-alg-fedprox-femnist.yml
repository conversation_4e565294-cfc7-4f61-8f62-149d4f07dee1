# Example config file for fl-sim command line interface

strategy:
  matrix:
    algorithm:
    - Ditto
    - FedDR
    - FedAvg
    - FedAdam
    - FedYogi
    - FedAdagrad
    - FedProx
    - FedPD
    - FedSplit
    - IFCA
    - pFedMac
    - pFedMe
    - ProxSkip
    - SCAFFOLD
    - FedDyn
    - APFL
    clients_sample_ratio:
    - 0.1
    - 0.3
    - 0.7
    - 1.0
    seed:
    - 0
    - 1
    - 2
    - 3
    - 4
  n_parallel: 1  # number of parallel jobs, not supported yet but kept for future use

algorithm:
  name: ${{ matrix.algorithm }}
  server:
    num_clients: null
    clients_sample_ratio: ${{ matrix.clients_sample_ratio }}
    num_iters: 100
    p: ${{ matrix.clients_sample_ratio }}  # for FedPD, ProxSkip
    lr: 0.003  # for SCAFFOLD, FedOpt (FedAdam, FedYogi, FedAdagrad, etc.), this parameter is quite sensitive
    num_clusters: 10  # for IFCA
    log_dir: all-alg-fedprox-femnist
    tag: sample_ratio_${{ matrix.clients_sample_ratio }}-seed_${{ matrix.seed }}
  client:
    lr: 0.03
    num_epochs: 10
    batch_size: null  # null for default batch size
    scheduler:
      name: step  # StepLR
      step_size: 1
      gamma: 0.99
dataset:
  name: FedProxFEMNIST
  datadir: null  # default dir
  transform: none  # none for static transform (only normalization, no augmentation)
model:
  name: cnn_femmist_tiny
seed: ${{ matrix.seed }}
