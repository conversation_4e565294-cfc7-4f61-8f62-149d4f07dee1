# Example config file for demonstrating multiprocessing output management

strategy:
  matrix:
    algorithm:
    - FedAvg
    clients_sample_ratio:
    - 0.3
    - 0.7
  parallel:
    mode: parallel_task
    num_workers: 2

mode: federated

seed: 42

dataset:
  name: FedProxFEMNIST
  data_dir: null  # will use default
  transform: none  # none for static transform (only normalization, no augmentation)

model:
  name: cnn_femmist_tiny

algorithm:
  name: ${{ matrix.algorithm }}
  server:
    num_iters: 50
    num_clients: null  # will use default
    clients_sample_ratio: ${{ matrix.clients_sample_ratio }}
    log_dir: multiprocess-test
    tag: algorithm_${{ matrix.algorithm }}-clients_sample_ratio_${{ matrix.clients_sample_ratio }}-seed_${{ seed }}
  client:
    num_epochs: 2
    batch_size: null  # will use default
