@inproceedings{deng2009imagenet,
         title = {{ImageNet: A Large-Scale Hierarchical Image Database}},
        author = {<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON>},
     booktitle = {{2009 IEEE Conference on Computer Vision and Pattern Recognition}},
         pages = {248--255},
          year = {2009},
  organization = {{IEEE}},
           doi = {10.1109/cvpr.2009.5206848}
}
@inproceedings{tensorflow,
      title = {{Tensorflow: A System for Large-Scale Machine Learning}},
     author = {<PERSON> and <PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON>},
  booktitle = {12th {USENIX} Symposium on Operating Systems Design and Implementation ({OSDI} 16)},
      pages = {265--283},
       year = {2016}
}
@inproceedings{pytorch,
      title = {{PyTorch: An Imperative Style, High-Performance Deep Learning Library}},
     author = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>n and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>a, <PERSON>th},
  booktitle = {{<PERSON> in <PERSON>eural <PERSON>ing Systems}},
     editor = {<PERSON>. <PERSON> and <PERSON>. <PERSON>le and A. Beygelzimer and F. d\textquotesingle Alch\'{e}-Buc and E. Fox and R. Garnett},
   location = {{Vancouver, BC, Canada}},
  articleno = {721},
   numpages = {12},
     volume = {32},
      pages = {8024--8035},
       year = {2019},
  publisher = {{Curran Associates, Inc.}},
    address = {{Red Hook, NY, USA}}
}
@inproceedings{dosovitskiy2021_vit,
      title = {{An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale}},
     author = {Alexey Dosovitskiy and Lucas Beyer and Alexander Kolesnikov and Dirk Weissenborn and Xiaohua Zhai and Thomas Unterthiner and Mostafa Dehghani and Matthias Minderer and Georg Heigold and Sylvain Gelly and Jakob Uszkoreit and Neil Houlsby},
  booktitle = {International Conference on Learning Representations},
       year = {2021},
        url = {https://openreview.net/forum?id=YicbFdNTTy}
}
@inproceedings{adam,
      title = {{Adam: A Method for Stochastic Optimization}},
     author = {Diederik P. Kingma and Jimmy Ba},
  booktitle = {{3rd International Conference on Learning Representations, ICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings}},
       year = {2015}
}
@article{kairouz2019advances_fl,
    title = {{Advances and Open Problems in Federated Learning}},
   author = {Peter Kairouz and H. Brendan McMahan and Brendan Avent and Aurélien Bellet and Mehdi Bennis and Arjun Nitin Bhagoji and Kallista Bonawitz and Zachary Charles and Graham Cormode and Rachel Cummings and Rafael G. L. D’Oliveira and Hubert Eichner and Salim El Rouayheb and David Evans and Josh Gardner and Zachary Garrett and Adrià Gascón and Badih Ghazi and Phillip B. Gibbons and Marco Gruteser and Zaid Harchaoui and Chaoyang He and Lie He and Zhouyuan Huo and Ben Hutchinson and Justin Hsu and Martin Jaggi and Tara Javidi and Gauri Joshi and Mikhail Khodak and Jakub Konecný and Aleksandra Korolova and Farinaz Koushanfar and Sanmi Koyejo and Tancrède Lepoint and Yang Liu and Prateek Mittal and Mehryar Mohri and Richard Nock and Ayfer Özgür and Rasmus Pagh and Hang Qi and Daniel Ramage and Ramesh Raskar and Mariana Raykova and Dawn Song and Weikang Song and Sebastian U. Stich and Ziteng Sun and Ananda Theertha Suresh and Florian Tramèr and Praneeth Vepakomma and Jianyu Wang and Li Xiong and Zheng Xu and Qiang Yang and Felix X. Yu and Han Yu and Sen Zhao},
  journal = {{Foundations and Trends{\textregistered} in Machine Learning}},
     year = {2021},
   volume = {14},
     issn = {1935-8237},
   number = {1–2},
    pages = {1-210},
      doi = {10.1561/2200000083}
}
@book{boyd2011distributed,
      title = {{Distributed Optimization and Statistical Learning via the Alternating Direction Method of Multipliers}},
     author = {Boyd, Stephen and Parikh, Neal and Chu, Eric},
       year = {2011},
  publisher = {{Now Publishers Inc.}}
}
@article{Parikh_2014_pa,
      title = {{Proximal Algorithms}},
     author = {Parikh, Neal and Boyd, Stephen},
    journal = {{Foundations and Trends{\textregistered} in Optimization}},
        doi = {10.1561/2400000003},
       year = {2014},
  publisher = {{Now Publishers}},
     volume = {1},
     number = {3},
      pages = {127--239}
}
@article{elgabli2020gadmm,
    title = {{GADMM: Fast and Communication Efficient Framework for Distributed Machine Learning}},
   author = {Elgabli, Anis and Park, Jihong and Bedi, Amrit S and Bennis, Mehdi and Aggarwal, Vaneet},
  journal = {{Journal of Machine Learning Research}},
   volume = {21},
   number = {76},
    pages = {1--39},
     year = {2020}
}
@article{issaid2020cq-ggadmm,
    title = {{Communication Efficient Distributed Learning with Censored, Quantized, and Generalized Group ADMM}},
   author = {Issaid, Chaouki Ben and Elgabli, Anis and Park, Jihong and Bennis, Mehdi},
  journal = {{arXiv preprint arXiv:2009.06459}},
     year = {2020}
}
@article{Robbins_1951_sgd,
      title = {{A Stochastic Approximation Method}},
     author = {Herbert Robbins and Sutton Monro},
    journal = {{The Annals of Mathematical Statistics}},
        doi = {10.1214/aoms/1177729586},
       year = {1951},
      month = {9},
  publisher = {{Institute of Mathematical Statistics}},
     volume = {22},
     number = {3},
      pages = {400--407}
}
@article{Peaceman_1955_prsm,
      title = {{The Numerical Solution of Parabolic and Elliptic Differential Equations}},
     author = {D. W. Peaceman and H. H. Rachford},
    journal = {{Journal of the Society for Industrial and Applied Mathematics}},
     number = {1},
      pages = {28--41},
  publisher = {{Society for Industrial and Applied Mathematics}},
     volume = {3},
       issn = {03684245},
       year = {1955}
}
@article{Douglas_1956_drsm,
      title = {{On the Numerical Solution of Heat Conduction Problems in Two and Three Space Variables}},
     author = {Jim Douglas and H. H. Rachford},
    journal = {{Transactions of the American Mathematical Society}},
        doi = {10.1090/s0002-9947-1956-0084194-4},
       year = {1956},
  publisher = {{American Mathematical Society (AMS)}},
     volume = {82},
     number = {2},
      pages = {421--439}
}
@article{Moreau_1965_prox,
      title = {{Proximité et Dualité dans un Espace Hilbertien}},
     author = {J.J. Moreau},
    journal = {{Bulletin de la Société Mathématique de France}},
        doi = {10.24033/bsmf.1625},
       year = {1965},
  publisher = {{Société Mathématique de France}},
     volume = {79},
      pages = {273--299}
}
@article{Lions_1979_splitting,
      title = {{Splitting Algorithms for the Sum of Two Nonlinear Operators}},
     author = {P. L. Lions and B. Mercier},
    journal = {{SIAM Journal on Numerical Analysis}},
        doi = {10.1137/0716071},
       year = {1979},
      month = {12},
  publisher = {{Society for Industrial \& Applied Mathematics (SIAM)}},
     volume = {16},
     number = {6},
      pages = {964--979}
}
@article{Mangasarian_1995_parallel,
      title = {{Parallel Gradient Distribution in Unconstrained Optimization}},
     author = {L. O. Mangasarian},
    journal = {{SIAM Journal on Control and Optimization}},
        doi = {10.1137/s0363012993250220},
       year = {1995},
      month = {11},
  publisher = {{Society for Industrial \& Applied Mathematics (SIAM)}},
     volume = {33},
     number = {6},
      pages = {1916--1925}
}
@article{adagrad,
    title = {{Adaptive Subgradient Methods for Online Learning and Stochastic Optimization}},
   author = {John Duchi and Elad Hazan and Yoram Singer},
  journal = {{Journal of Machine Learning Research}},
     year = {2011},
    month = {7},
   volume = {12},
   number = {61},
    pages = {2121--2159}
}
@inproceedings{Zaheer_2018_yogi,
      title = {{Adaptive Methods for Nonconvex Optimization}},
     author = {Zaheer, Manzil and Reddi, Sashank J. and Sachan, Devendra and Kale, Satyen and Kumar, Sanjiv},
  booktitle = {{Proceedings of the 32nd International Conference on Neural Information Processing Systems}},
       year = {2018},
  publisher = {{Curran Associates Inc.}},
    address = {Red Hook, NY, USA},
      pages = {9815–9825},
   numpages = {11},
   location = {Montr\'{e}al, Canada},
     series = {{NIPS'18}}
}
@inproceedings{mcmahan2017fed_avg,
         title = {{Communication-Efficient Learning of Deep Networks from Decentralized Data}},
        author = {McMahan, Brendan and Moore, Eider and Ramage, Daniel and Hampson, Seth and y Arcas, Blaise Aguera},
     booktitle = {{Artificial Intelligence and Statistics}},
         pages = {1273--1282},
          year = {2017},
  organization = {{PMLR}}
}
@inproceedings{Sun_2017_JFT-300M,
      title = {{Revisiting Unreasonable Effectiveness of Data in Deep Learning Era}},
     author = {Chen Sun and Abhinav Shrivastava and Saurabh Singh and Abhinav Gupta},
  booktitle = {{2017 IEEE International Conference on Computer Vision (ICCV)}},
        doi = {10.1109/iccv.2017.97},
      pages = {843-852},
       year = {2017},
      month = {10},
  publisher = {{IEEE}},
   location = {{Venice, Italy}}
}
@inproceedings{stich2018local,
      title = {{Local SGD Converges Fast and Communicates Little}},
     author = {Stich, Sebastian U},
  booktitle = {{International Conference on Learning Representations}},
       year = {2018},
  publisher = {{OpenReview.net}}
}
@article{khaled2019_first,
    title = {{First Analysis of Local GD on Heterogeneous Data}},
   author = {Ahmed Khaled and Konstantin Mishchenko and Peter Richtárik},
  journal = {{arXiv preprint arXiv:1909.04715v2}},
      doi = {10.48550/ARXIV.1909.04715},
     year = {2019},
    month = {9}
}
@inproceedings{Khaled2020_tighter,
      title = {{Tighter Theory for Local SGD on Identical and Heterogeneous Data}},
     author = {Khaled, Ahmed and Mishchenko, Konstantin and Richtárik, Peter},
  booktitle = {{Proceedings of the Twenty Third International Conference on Artificial Intelligence and Statistics}},
      pages = {4519--4529},
       year = {2020},
     editor = {Chiappa, Silvia and Calandra, Roberto},
     volume = {108},
     series = {{Proceedings of Machine Learning Research}},
      month = {8},
  publisher = {{PMLR}}
}
@inproceedings{li2019convergence,
      title = {{On the Convergence of FedAvg on Non-IID Data}},
     author = {Li, Xiang and Huang, Kaixuan and Yang, Wenhao and Wang, Shusen and Zhang, Zhihua},
  booktitle = {{International Conference on Learning Representations}},
       year = {2020},
  publisher = {{OpenReview.net}}
}
@inproceedings{reddi2020fed_opt,
      title = {{Adaptive Federated Optimization}},
     author = {Sashank J. Reddi and Zachary Charles and Manzil Zaheer and Zachary Garrett and Keith Rush and Jakub Kone{\v{c}}n{\'y} and Sanjiv Kumar and Hugh Brendan McMahan},
  booktitle = {{International Conference on Learning Representations}},
       year = {2021}
}
@article{hanzely2020federated,
    title = {{Federated Learning of a Mixture of Global and Local Models}},
   author = {Hanzely, Filip and Richt{\'a}rik, Peter},
  journal = {{arXiv preprint arXiv:2002.05516}},
     year = {2020}
}
@inproceedings{li_2021_ditto,
      title = {{Ditto: Fair and Robust Federated Learning Through Personalization}},
     author = {Li, Tian and Hu, Shengyuan and Beirami, Ahmad and Smith, Virginia},
  booktitle = {{Proceedings of the 38th International Conference on Machine Learning}},
      pages = {6357--6368},
       year = {2021},
     editor = {Meila, Marina and Zhang, Tong},
     volume = {139},
     series = {{Proceedings of Machine Learning Research}},
      month = {7},
  publisher = {{PMLR}}
}
@inproceedings{sahu2018fedprox,
      title = {{Federated Optimization in Heterogeneous Networks}},
     author = {Li, Tian and Sahu, Anit Kumar and Zaheer, Manzil and Sanjabi, Maziar and Talwalkar, Ameet and Smith, Virginia},
  booktitle = {{Proceedings of Machine Learning and Systems}},
     editor = {I. Dhillon and D. Papailiopoulos and V. Sze},
      pages = {429--450},
     volume = {2},
       year = {2020}
}
@inproceedings{karimireddy2020scaffold,
         title = {{SCAFFOLD: Stochastic Controlled Averaging for Federated Learning}},
        author = {Karimireddy, Sai Praneeth and Kale, Satyen and Mohri, Mehryar and Reddi, Sashank and Stich, Sebastian and Suresh, Ananda Theertha},
     booktitle = {{International Conference on Machine Learning}},
         pages = {5132--5143},
          year = {2020},
  organization = {{PMLR}}
}
@inproceedings{t2020pfedme,
      title = {{Personalized Federated Learning with Moreau Envelopes}},
     author = {Dinh, Canh T. and Tran, Nguyen H. and Nguyen, Tuan Dung},
  booktitle = {{Proceedings of the 34th International Conference on Neural Information Processing Systems}},
  articleno = {1796},
   numpages = {12},
       year = {2020},
   location = {Vancouver, BC, Canada},
       isbn = {9781713829546},
  publisher = {Curran Associates Inc.},
    address = {Red Hook, NY, USA}
}
@inproceedings{smith2017mocha,
      title = {{Federated Multi-Task Learning}},
     author = {Smith, Virginia and Chiang, Chao-Kai and Sanjabi, Maziar and Talwalkar, Ameet},
  booktitle = {{Proceedings of the 31st International Conference on Neural Information Processing Systems}},
      pages = {4427--4437},
       year = {2017}
}
@article{dinh2021fedu,
    title = {{FedU: A Unified Framework for Federated Multi-Task Learning with Laplacian Regularization}},
   author = {Dinh, Canh T and Vu, Tung T and Tran, Nguyen H and Dao, Minh N and Zhang, Hongyu},
  journal = {{arXiv preprint arXiv:2102.07148}},
     year = {2021}
}
@article{jiang2019improving,
    title = {{Improving Federated Learning Personalization via Model Agnostic Meta Learning}},
   author = {Jiang, Yihan and Kone{\v{c}}n{\`y}, Jakub and Rush, Keith and Kannan, Sreeram},
  journal = {{arXiv preprint arXiv:1909.12488}},
     year = {2019}
}
@article{fallah2020personalized,
    title = {{Personalized Federated Learning: A Meta-Learning Approach}},
   author = {Fallah, Alireza and Mokhtari, Aryan and Ozdaglar, Asuman},
  journal = {{arXiv preprint arXiv:2002.07948}},
     year = {2020}
}
@inproceedings{seide2014_1bitsgd,
      title = {{1-Bit Stochastic Gradient Descent and Application to Data-Parallel Distributed Training of Speech DNNs}},
     author = {Seide, Frank and Fu, Hao and Droppo, Jasha and Li, Gang and Yu, Dong},
  booktitle = {{Interspeech 2014}},
       year = {2014},
      month = {9}
}
@article{zhang2020fedpd,
      title = {{FedPD: A Federated Learning Framework With Adaptivity to Non-IID Data}},
     author = {Xinwei Zhang and Mingyi Hong and Sairaj Dhople and Wotao Yin and Yang Liu},
    journal = {{IEEE Transactions on Signal Processing}},
        doi = {10.1109/tsp.2021.3115952},
       year = {2021},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {69},
      pages = {6055--6070}
}
@article{Lecun_1998_mnist,
      title = {{Gradient-Based Learning Applied to Document Recognition}},
     author = {Y. Lecun and L. Bottou and Y. Bengio and P. Haffner},
    journal = {{Proceedings of the IEEE}},
        doi = {10.1109/5.726791},
       year = {1998},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {86},
     number = {11},
      pages = {2278--2324}
}
@inproceedings{cohen2017emnist,
         title = {{EMNIST: Extending MNIST to Handwritten Letters}},
        author = {Cohen, Gregory and Afshar, Saeed and Tapson, Jonathan and Van Schaik, Andre},
     booktitle = {{2017 International Joint Conference on Neural Networks (IJCNN)}},
         pages = {2921--2926},
          year = {2017},
           doi = {10.1109/ijcnn.2017.7966217},
  organization = {{IEEE}}
}
@misc{nist-19,
      title = {{NIST Handprinted Forms and Characters - NIST Special Database 19}},
     author = {{Patricia A. Flanagan}},
        doi = {10.18434/T4H01C},
   language = {en},
  publisher = {{National Institute of Standards and Technology}},
       year = {2016},
  copyright = {{License Information for NIST data}}
}
@inproceedings{Li_2006_LDA,
      title = {{Pachinko Allocation: DAG-Structured Mixture Models of Topic Correlations}},
     author = {Wei Li and Andrew McCallum},
  booktitle = {{Proceedings of the 23rd International Conference on Machine Learning}},
      pages = {577–584},
   numpages = {8},
   location = {{Pittsburgh, Pennsylvania, USA}},
     series = {{ICML '06}},
        doi = {10.1145/1143844.1143917},
       year = {2006},
  publisher = {{Association for Computing Machinery}},
    address = {{New York, NY, USA}}
}
@article{zhu2019deep_leakage,
    title = {{Deep Leakage from Gradients}},
   author = {Zhu, Ligeng and Liu, Zhijian and Han, Song},
  journal = {{Advances in Neural Information Processing Systems}},
   volume = {32},
    pages = {14774--14784},
     year = {2019}
}
@article{li2021pfedmac,
    title = {{Personalized Federated Learning via Maximizing Correlation with Sparse and Hierarchical Extensions}},
   author = {Li, Yinchuan and Liu, Xiaofeng and Zhang, Xu and Shao, Yunfeng and Wang, Qing and Geng, Yanhui},
  journal = {{arXiv preprint arXiv:2107.05330}},
     year = {2021}
}
@inproceedings{adamw_amsgrad,
      title = {{On the Convergence of Adam and Beyond}},
     author = {Sashank J. Reddi and Satyen Kale and Sanjiv Kumar},
  booktitle = {{6th International Conference on Learning Representations (ICLR)}},
   location = {{Vancouver, BC, Canada}},
  publisher = {{OpenReview.net}},
       year = {2018},
      month = {5},
        url = {https://openreview.net/forum?id=ryQu7f-RZ}
}
@inproceedings{pathak2020fedsplit,
      title = {{FedSplit: An Algorithmic Framework for Fast Federated Optimization}},
     author = {Pathak, Reese and Wainwright, Martin J},
  booktitle = {Advances in Neural Information Processing Systems},
     editor = {H. Larochelle and M. Ranzato and R. Hadsell and M.F. Balcan and H. Lin},
      pages = {7057--7066},
  publisher = {Curran Associates, Inc.},
     volume = {33},
       year = {2020}
}
@inproceedings{tran2021feddr,
      title = {{FedDR {\textendash} Randomized Douglas-Rachford Splitting Algorithms for Nonconvex Federated Composite Optimization}},
     author = {Quoc Tran-Dinh and Nhan Pham and Dzung T. Phan and Lam M. Nguyen},
  booktitle = {Advances in Neural Information Processing Systems},
     editor = {A. Beygelzimer and Y. Dauphin and P. Liang and J. Wortman Vaughan},
       year = {2021}
}
@inproceedings{Dinh_2020_FL_PSVRG,
      title = {{Federated Learning with Proximal Stochastic Variance Reduced Gradient Algorithms}},
     author = {Canh T. Dinh and Nguyen H. Tran and Tuan Dung Nguyen and Wei Bao and Albert Y. Zomaya and Bing B. Zhou},
  booktitle = {{49th International Conference on Parallel Processing - ICPP}},
        doi = {10.1145/3404397.3404457},
       year = {2020},
      month = {8},
  publisher = {{Association for Computing Machinery}}
}
@inproceedings{johnson2013accelerating,
      title = {{Accelerating Stochastic Gradient Descent using Predictive Variance Reduction}},
     author = {Johnson, Rie and Zhang, Tong},
  booktitle = {{Advances in Neural Information Processing Systems}},
     editor = {C.J. Burges and L. Bottou and M. Welling and Z. Ghahramani and K.Q. Weinberger},
  publisher = {{Curran Associates, Inc.}},
     volume = {26},
       year = {2013}
}
@article{Caruana_1997_mtl,
      title = {{Multitask Learning}},
     author = {Rich Caruana},
    journal = {{Machine Learning}},
        doi = {10.1023/a:1007379606734},
       year = {1997},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {28},
     number = {1},
      pages = {41--75}
}
@article{Sattler_2021_cfl,
      title = {{Clustered Federated Learning: Model-Agnostic Distributed Multitask Optimization Under Privacy Constraints}},
     author = {Felix Sattler and Klaus-Robert Muller and Wojciech Samek},
    journal = {{IEEE Transactions on Neural Networks and Learning Systems}},
        doi = {10.1109/tnnls.2020.3015958},
       year = {2021},
      month = {8},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {32},
     number = {8},
      pages = {3710--3722}
}
@article{fl_fpfc_2022,
      title = {{Clustered Federated Learning based on Nonconvex Pairwise Fusion}},
     author = {Yu, Xue and Liu, Ziyi and Sun, Yifan and Wang, Wu},
        doi = {10.48550/ARXIV.2211.04218},
  publisher = {{arXiv}},
       year = {2022}
}
@article{Ghosh_2022_cfl,
      title = {{An Efficient Framework for Clustered Federated Learning}},
     author = {Avishek Ghosh and Jichan Chung and Dong Yin and Kannan Ramchandran},
    journal = {{IEEE Transactions on Information Theory}},
        doi = {10.1109/tit.2022.3192506},
       year = {2022},
      month = {12},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {68},
     number = {12},
      pages = {8076--8091}
}
@article{Zhang_2023_fedmds,
      title = {{FedMDS: An Efficient Model Discrepancy-Aware Semi-Asynchronous Clustered Federated Learning Framework}},
     author = {Yu Zhang and Duo Liu and Moming Duan and Li Li and Xianzhang Chen and Ao Ren and Yujuan Tan and Chengliang Wang},
    journal = {{IEEE Transactions on Parallel and Distributed Systems}},
        doi = {10.1109/tpds.2023.3237752},
       year = {2023},
      month = {3},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {34},
     number = {3},
      pages = {1007--1019}
}
@inproceedings{evgeniou2004regularized,
      title = {{Regularized Multi--Task Learning}},
     author = {Evgeniou, Theodoros and Pontil, Massimiliano},
  booktitle = {{Proceedings of the Tenth ACM SIGKDD International Conference on Knowledge Discovery and Data Mining}},
      pages = {109--117},
       year = {2004}
}
@article{Albrecht_2016,
      title = {{How the GDPR Will Change the World}},
     author = {J.P. Albrecht},
    journal = {{European Data Protection Law Review}},
        doi = {10.21552/edpl/2016/3/4},
       year = {2016},
  publisher = {{Lexxion Verlag}},
     volume = {2},
     number = {3},
      pages = {287--289}
}
@misc{fl_keyboard,
      title = {{Federated Learning for Mobile Keyboard Prediction}},
     author = {Hard, Andrew and Rao, Kanishka and Mathews, Rajiv and Ramaswamy, Swaroop and Beaufays, Françoise and Augenstein, Sean and Eichner, Hubert and Kiddon, Chloé and Ramage, Daniel},
        doi = {10.48550/ARXIV.1811.03604},
  publisher = {{arXiv}},
       year = {2018}
}
@inproceedings{Dwork_2008_DP,
      title = {{Differential Privacy: A Survey of Results}},
     author = {Cynthia Dwork},
  booktitle = {{International Conference on Theory and Applications of Models of Computation}},
     editor = {Agrawal, Manindra and Du, Dingzhu and Duan, Zhenhua and Li, Angsheng},
        doi = {10.1007/978-3-540-79228-4_1},
  publisher = {{Springer Berlin Heidelberg}},
      pages = {1--19},
       year = {2008}
}
@article{vfl,
      title = {{Vertical Federated Learning}},
     author = {Liu, Yang and Kang, Yan and Zou, Tianyuan and Pu, Yanhong and He, Yuanqin and Ye, Xiaozhou and Ouyang, Ye and Zhang, Ya-Qin and Yang, Qiang},
        doi = {10.48550/ARXIV.2211.12814},
  publisher = {{arXiv}},
       year = {2022}
}
@article{liu_2020_transfer_fl,
      title = {{A Secure Federated Transfer Learning Framework}},
     author = {Yang Liu and Yan Kang and Chaoping Xing and Tianjian Chen and Qiang Yang},
    journal = {{IEEE Intelligent Systems}},
        doi = {10.1109/mis.2020.2988525},
       year = {2020},
      month = {7},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {35},
     number = {4},
      pages = {70--82}
}
@inproceedings{dist_pca_2014_nips,
      title = {{Improved Distributed Principal Component Analysis}},
     author = {Balcan, Maria-Florina and Kanchanapally, Vandana and Liang, Yingyu and Woodruff, David},
  booktitle = {{Proceedings of the 27th International Conference on Neural Information Processing Systems - Volume 2}},
       year = {2014},
  publisher = {{MIT Press}},
    address = {{Cambridge, MA, USA}},
      pages = {3113–3121},
   numpages = {9},
   location = {{Montreal, Canada}},
     series = {{NIPS'14}}
}
@inproceedings{Gentry_2009_FHE,
      title = {{Fully Homomorphic Encryption using Ideal Lattices}},
     author = {Craig Gentry},
  booktitle = {{Proceedings of the Forty-first Annual ACM Symposium on Theory of Computing}},
        doi = {10.1145/1536414.1536440},
      pages = {169–178},
       year = {2009},
      month = {5},
  publisher = {{Association for Computing Machinery}},
   location = {{Bethesda, MD, USA}},
     series = {{STOC'09}}
}
@inproceedings{Nikolaenko_2013,
      title = {{Privacy-Preserving Ridge Regression on Hundreds of Millions of Records}},
     author = {V. Nikolaenko and U. Weinsberg and S. Ioannidis and M. Joye and D. Boneh and N. Taft},
  booktitle = {{2013 IEEE Symposium on Security and Privacy}},
        doi = {10.1109/sp.2013.30},
   location = {{Berkeley, CA, US}},
       year = {2013},
      month = {5},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}}
}
@article{Yang_2019_VFL,
      title = {{Federated Machine Learning: Concept and Applications}},
     author = {Yang, Qiang and Liu, Yang and Chen, Tianjian and Tong, Yongxin},
    journal = {{ACM Transactions on Intelligent Systems and Technology}},
       year = {2019},
      month = {1},
  publisher = {{Association for Computing Machinery}},
    address = {{New York, NY, USA}},
     volume = {10},
     number = {2},
       issn = {2157-6904},
        doi = {10.1145/3298981},
  articleno = {12},
      pages = {1--19},
   numpages = {19}
}
@article{chen2016_revisit,
    title = {{Revisiting Distributed Synchronous SGD}},
   author = {Chen, Jianmin and Pan, Xinghao and Monga, Rajat and Bengio, Samy and Jozefowicz, Rafal},
  journal = {{arXiv preprint arXiv:1604.00981v3}},
      doi = {10.48550/ARXIV.1604.00981},
     year = {2016},
    month = {4}
}
@inproceedings{proxskip,
      title = {{ProxSkip: Yes! Local Gradient Steps Provably Lead to Communication Acceleration! Finally!}},
     author = {Mishchenko, Konstantin and Malinovsky, Grigory and Stich, Sebastian and Richtarik, Peter},
  booktitle = {{Proceedings of the 39th International Conference on Machine Learning}},
      pages = {15750--15769},
       year = {2022},
     editor = {Chaudhuri, Kamalika and Jegelka, Stefanie and Song, Le and Szepesvari, Csaba and Niu, Gang and Sabato, Sivan},
     volume = {162},
     series = {{Proceedings of Machine Learning Research}},
      month = {7},
  publisher = {{PMLR}}
}
@inproceedings{proxskip-vr,
      title = {{ProxSkip for Stochastic Variational Inequalities: A Federated Learning Algorithm for Provable Communication Acceleration}},
     author = {Siqi Zhang and Nicolas Loizou},
  booktitle = {{OPT 2022: Optimization for Machine Learning (NeurIPS 2022 Workshop)}},
       year = {2022}
}
@article{Huang_2022_fl_physics,
      title = {{Federated Data Processing and Learning for Collaboration in the Physical Sciences}},
     author = {W Huang and A S Barnard},
    journal = {{Machine Learning: Science and Technology}},
        doi = {10.1088/2632-2153/aca87c},
       year = {2022},
      month = {12},
  publisher = {{IOP Publishing}},
     volume = {3},
     number = {4},
      pages = {045023}
}
@article{Antunes_2022_fl_healthcare,
      title = {{Federated Learning for Healthcare: Systematic Review and Architecture Proposal}},
     author = {Rodolfo Stoffel Antunes and Cristiano Andr{\'{e}} da Costa and Arne Küderle and Imrana Abdullahi Yari and Björn Eskofier},
    journal = {{ACM Transactions on Intelligent Systems and Technology}},
        doi = {10.1145/3501813},
       year = {2022},
      month = {5},
  publisher = {{Association for Computing Machinery (ACM)}},
     volume = {13},
     number = {4},
      pages = {1--23}
}
@article{rauniyar2022_fl_medical,
    title = {{Federated Learning for Medical Applications: A Taxonomy, Current Trends, Challenges, and Future Research Directions}},
   author = {Rauniyar, Ashish and Hagos, Desta Haileselassie and Jha, Debesh and Håkegård, Jan Erik and Bagci, Ulas and Rawat, Danda B. and Vlassov, Vladimir},
  journal = {{arXiv preprint arXiv:2208.03392v3}},
      doi = {10.48550/ARXIV.2208.03392},
     year = {2022},
    month = {8}
}
@inproceedings{Schreyer_2022_fl_audits,
      title = {{Federated and Privacy-Preserving Learning of Accounting Data in Financial Statement Audits}},
     author = {Marco Schreyer and Timur Sattarov and Damian Borth},
  booktitle = {{3rd ACM International Conference on AI in Finance}},
        doi = {10.1145/3533271.3561674},
       year = {2022},
      month = {10},
  publisher = {{Association for Computing Machinery (ACM)}}
}
@article{liu_2021_fate_fl,
      title = {{FATE: An Industrial Grade Platform for Collaborative Learning With Data Protection}},
     author = {Yang Liu and Tao Fan and Tianjian Chen and Qian Xu and Qiang Yang},
    journal = {{Journal of Machine Learning Research}},
  publisher = {{JMLR.org}},
     number = {1},
       issn = {1532-4435},
       year = {2021},
      month = {1},
  articleno = {226},
   numpages = {6},
     volume = {22},
      pages = {10320–10325}
}
@article{Toyotaro_2019_fl,
    title = {{Towards Federated Graph Learning for Collaborative Financial Crimes Detection}},
   author = {Suzumura, Toyotaro and Zhou, Yi and Baracaldo, Natahalie and Ye, Guangnan and Houck, Keith and Kawahara, Ryo and Anwar, Ali and Stavarache, Lucia Larise and Watanabe, Yuji and Loyola, Pablo and Klyashtorny, Daniel and Ludwig, Heiko and Bhaskaran, Kumar},
  journal = {{arXiv preprint arXiv:1909.12946v2}},
      doi = {10.48550/ARXIV.1909.12946},
     year = {2019},
    month = {9}
}
@inproceedings{Lv_2021_fl,
      title = {{Research on Modeling of E-banking Fraud Account Identification Based on Federated Learning}},
     author = {Boliang Lv and Peizhe Cheng and Cheng Zhang and Hong Ye and Xianzhe Meng and Xiao Wang},
  booktitle = {{2021 IEEE Intl Conf on Dependable, Autonomic and Secure Computing, Intl Conf on Pervasive Intelligence and Computing, Intl Conf on Cloud and Big Data Computing, Intl Conf on Cyber Science and Technology Congress (DASC/PiCom/CBDCom/CyberSciTech)}},
        doi = {10.1109/dasc-picom-cbdcom-cyberscitech52372.2021.00105},
       year = {2021},
      month = {10},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}}
}
@article{Imteaj_2022_fl,
      title = {{Leveraging Asynchronous Federated Learning to Predict Customers Financial Distress}},
     author = {Ahmed Imteaj and M. Hadi Amini},
    journal = {{Intelligent Systems with Applications}},
        doi = {10.1016/j.iswa.2022.200064},
       year = {2022},
      month = {5},
  publisher = {{Elsevier BV}},
     volume = {14},
      pages = {200064}
}
@article{Dayan_2021_fl_covid,
      title = {{Federated Learning for Predicting Clinical Outcomes in Patients with COVID-19}},
     author = {Ittai Dayan and Holger R. Roth and Aoxiao Zhong and Ahmed Harouni and Amilcare Gentili and Anas Z. Abidin and Andrew Liu and Anthony Beardsworth Costa and Bradford J. Wood and Chien-Sung Tsai and Chih-Hung Wang and Chun-Nan Hsu and C. K. Lee and Peiying Ruan and Daguang Xu and Dufan Wu and Eddie Huang and Felipe Campos Kitamura and Griffin Lacey and Gustavo C{\'{e}}sar de Ant{\^{o}}nio Corradi and Gustavo Nino and Hao-Hsin Shin and Hirofumi Obinata and Hui Ren and Jason C. Crane and Jesse Tetreault and Jiahui Guan and John W. Garrett and Joshua D. Kaggie and Jung Gil Park and Keith Dreyer and Krishna Juluru and Kristopher Kersten and Marcio Aloisio Bezerra Cavalcanti Rockenbach and Marius George Linguraru and Masoom A. Haider and Meena AbdelMaseeh and Nicola Rieke and Pablo F. Damasceno and Pedro Mario Cruz e Silva and Pochuan Wang and Sheng Xu and Shuichi Kawano and Sira Sriswasdi and Soo Young Park and Thomas M. Grist and Varun Buch and Watsamon Jantarabenjakul and Weichung Wang and Won Young Tak and Xiang Li and Xihong Lin and Young Joon Kwon and Abood Quraini and Andrew Feng and Andrew N. Priest and Baris Turkbey and Benjamin Glicksberg and Bernardo Bizzo and Byung Seok Kim and Carlos Tor-D{\'{\i}}ez and Chia-Cheng Lee and Chia-Jung Hsu and Chin Lin and Chiu-Ling Lai and Christopher P. Hess and Colin Compas and Deepeksha Bhatia and Eric K. Oermann and Evan Leibovitz and Hisashi Sasaki and Hitoshi Mori and Isaac Yang and Jae Ho Sohn and Krishna Nand Keshava Murthy and Li-Chen Fu and Matheus Ribeiro Furtado de Mendon{\c{c}}a and Mike Fralick and Min Kyu Kang and Mohammad Adil and Natalie Gangai and Peerapon Vateekul and Pierre Elnajjar and Sarah Hickman and Sharmila Majumdar and Shelley L. McLeod and Sheridan Reed and Stefan Gräf and Stephanie Harmon and Tatsuya Kodama and Thanyawee Puthanakit and Tony Mazzulli and Vitor Lima de Lavor and Yothin Rakvongthai and Yu Rim Lee and Yuhong Wen and Fiona J. Gilbert and Mona G. Flores and Quanzheng Li},
    journal = {{Nature Medicine}},
        doi = {10.1038/s41591-021-01506-3},
       year = {2021},
      month = {9},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {27},
     number = {10},
      pages = {1735--1743}
}
@article{Brisimi_2018_fl_ehr,
      title = {{Federated Learning of Predictive Models from Federated Electronic Health Records}},
     author = {Theodora S. Brisimi and Ruidi Chen and Theofanie Mela and Alex Olshevsky and Ioannis Ch. Paschalidis and Wei Shi},
    journal = {{International Journal of Medical Informatics}},
        doi = {10.1016/j.ijmedinf.2018.01.007},
       year = {2018},
      month = {4},
  publisher = {{Elsevier BV}},
     volume = {112},
      pages = {59--67}
}
@article{Li_2020_fl_mri,
      title = {{Multi-Site fMRI Analysis using Privacy-Preserving Federated Learning and Domain Adaptation: ABIDE Results}},
     author = {Xiaoxiao Li and Yufeng Gu and Nicha Dvornek and Lawrence H. Staib and Pamela Ventola and James S. Duncan},
    journal = {{Medical Image Analysis}},
        doi = {10.1016/j.media.2020.101765},
       year = {2020},
      month = {10},
  publisher = {{Elsevier BV}},
     volume = {65},
      pages = {101765}
}
@inproceedings{Leroy_2019_fl_ks,
      title = {{Federated Learning for Keyword Spotting}},
     author = {David Leroy and Alice Coucke and Thibaut Lavril and Thibault Gisselbrecht and Joseph Dureau},
  booktitle = {{ICASSP 2019 - 2019 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)}},
        doi = {10.1109/icassp.2019.8683546},
       year = {2019},
      month = {5},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}}
}
@inproceedings{Sozinov_2018_fl_human,
      title = {{Human Activity Recognition Using Federated Learning}},
     author = {Konstantin Sozinov and Vladimir Vlassov and Sarunas Girdzijauskas},
  booktitle = {{2018 IEEE Intl Conf on Parallel & Distributed Processing with Applications, Ubiquitous Computing & Communications, Big Data & Cloud Computing, Social Computing & Networking, Sustainable Computing & Communications (ISPA/IUCC/BDCloud/SocialCom/SustainCom)}},
        doi = {10.1109/bdcloud.2018.00164},
       year = {2018},
      month = {12},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}}
}
@article{feng_2020_fl_pmf,
      title = {{PMF: A Privacy-Preserving Human Mobility Prediction Framework via Federated Learning}},
     author = {Feng, Jie and Rong, Can and Sun, Funing and Guo, Diansheng and Li, Yong},
    journal = {Proceedings of the {ACM} on Interactive, Mobile, Wearable and Ubiquitous Technologies},
  publisher = {{Association for Computing Machinery (ACM)}},
    address = {New York, NY, USA},
     volume = {4},
     number = {1},
        doi = {10.1145/3381006},
       year = {2020},
      month = {3},
  articleno = {10},
   numpages = {21},
      pages = {1--21}
}
@article{Zheng_2021_fl_smart_city,
      title = {{Applications of Federated Learning in smart cities: recent advances, taxonomy, and open challenges}},
     author = {Zhaohua Zheng and Yize Zhou and Yilong Sun and Zhang Wang and Boyi Liu and Keqiu Li},
    journal = {{Connection Science}},
        doi = {10.1080/09540091.2021.1936455},
       year = {2021},
      month = {6},
  publisher = {{Informa UK Limited}},
     volume = {34},
     number = {1},
      pages = {1--28}
}
@article{he_2020_fedml,
    title = {{FedML: A Research Library and Benchmark for Federated Machine Learning}},
   author = {Chaoyang He and Songze Li and Jinhyun So and Xiao Zeng and Mi Zhang and Hongyi Wang and Xiaoyang Wang and Praneeth Vepakomma and Abhishek Singh and Hang Qiu and Xinghua Zhu and Jianzong Wang and Li Shen and Peilin Zhao and Yan Kang and Yang Liu and Ramesh Raskar and Qiang Yang and Murali Annavaram and Salman Avestimehr},
  journal = {{arXiv preprint arXiv:2007.13518v4}},
     year = {2020},
    month = {7},
      doi = {10.48550/ARXIV.2007.13518}
}
@article{ryffel_2018_pysyft,
    title = {{A Generic Framework for Privacy Preserving Deep Learning}},
   author = {Theo Ryffel and Andrew Trask and Morten Dahl and Bobby Wagner and Jason Mancuso and Daniel Rueckert and Jonathan Passerat-Palmbach},
  journal = {{arXiv preprint arXiv:1811.04017v2}},
     year = {2018},
    month = {11},
      doi = {10.48550/ARXIV.1811.04017}
}
@article{wu_2021_fedsim,
    title = {{A Coupled Design of Exploiting Record Similarity for Practical Vertical Federated Learning}},
   author = {Zhaomin Wu and Qinbin Li and Bingsheng He},
  journal = {{arXiv preprint arXiv:2106.06312v2}},
     year = {2021},
    month = {6},
      doi = {10.48550/ARXIV.2106.06312}
}
@article{Li_2020_fl_challenges,
      title = {{Federated Learning: Challenges, Methods, and Future Directions}},
     author = {Tian Li and Anit Kumar Sahu and Ameet Talwalkar and Virginia Smith},
    journal = {{IEEE Signal Processing Magazine}},
        doi = {10.1109/msp.2020.2975749},
       year = {2020},
      month = {5},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {37},
     number = {3},
      pages = {50--60}
}
@incollection{Bogetoft_2009_smpc,
      title = {{Secure Multiparty Computation Goes Live}},
     author = {Peter Bogetoft and Dan Lund Christensen and Ivan Damg{\aa}rd and Martin Geisler and Thomas Jakobsen and Mikkel Kr{\o}igaard and Janus Dam Nielsen and Jesper Buus Nielsen and Kurt Nielsen and Jakob Pagter and Michael Schwartzbach and Tomas Toft},
  booktitle = {{Financial Cryptography and Data Security}},
        doi = {10.1007/978-3-642-03549-4_20},
       year = {2009},
  publisher = {{Springer Berlin Heidelberg}},
      pages = {325--343}
}
@inproceedings{zhou_2018_convergence,
      title = {{On the Convergence Properties of a K-Step Averaging Stochastic Gradient Descent Algorithm for Nonconvex Optimization}},
     author = {Zhou, Fan and Cong, Guojing},
  booktitle = {{Proceedings of the 27th International Joint Conference on Artificial Intelligence}},
       year = {2018},
       isbn = {9780999241127},
  publisher = {{AAAI Press}},
      pages = {3219–3227},
   numpages = {9},
   location = {Stockholm, Sweden},
     series = {{IJCAI'18}}
}
@inproceedings{Krishna_2022_partial_per_fl,
      title = {{Federated Learning with Partial Model Personalization}},
     author = {Pillutla, Krishna and Malik, Kshitiz and Mohamed, Abdel-Rahman and Rabbat, Mike and Sanjabi, Maziar and Xiao, Lin},
  booktitle = {{Proceedings of the 39th International Conference on Machine Learning}},
      pages = {17716--17758},
       year = {2022},
     editor = {Chaudhuri, Kamalika and Jegelka, Stefanie and Song, Le and Szepesvari, Csaba and Niu, Gang and Sabato, Sivan},
     volume = {162},
     series = {{Proceedings of Machine Learning Research}},
      month = {7},
  publisher = {{PMLR}}
}
@article{Wu_2020_iot_fl,
      title = {{Personalized Federated Learning for Intelligent IoT Applications: A Cloud-Edge Based Framework}},
     author = {Qiong Wu and Kaiwen He and Xu Chen},
    journal = {{IEEE Open Journal of the Computer Society}},
        doi = {10.1109/ojcs.2020.2993259},
       year = {2020},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {1},
      pages = {35--44}
}
@book{ryu2022large,
      title = {{Large-Scale Convex Optimization: Algorithms \& Analyses via Monotone Operators}},
     author = {Ryu, Ernest K and Yin, Wotao},
       year = {2022},
      month = {11},
        doi = {10.1017/9781009160865},
  publisher = {{Cambridge University Press}}
}
@book{nocedal_2006_num_opt,
      title = {{Numerical Optimization}},
     author = {Nocedal, Jorge and Wright, Stephen J.},
        doi = {10.1007/978-0-387-40065-5},
       year = {2006},
  publisher = {{Springer New York}}
}
@inproceedings{acar2021feddyn,
      title = {{Federated Learning Based on Dynamic Regularization}},
     author = {Durmus Alp Emre Acar and Yue Zhao and Ramon Matas and Matthew Mattina and Paul Whatmough and Venkatesh Saligrama},
  booktitle = {{International Conference on Learning Representations}},
       year = {2021}
}
@inproceedings{zhang2021fomo,
      title = {{Personalized Federated Learning with First Order Model Optimization}},
     author = {Michael Zhang and Karan Sapra and Sanja Fidler and Serena Yeung and Jose M. Alvarez},
  booktitle = {International Conference on Learning Representations},
       year = {2021}
}
@article{zarantonello1960solving,
      title = {{Solving Functional Equations by Contractive Averaging}},
     author = {Zarantonello, EH},
    journal = {Research Center Report},
  publisher = {{Mathematics Research Center, United States Army University of Wisconsin}},
     volume = {160},
       year = {1960}
}
@article{Minty_1962,
      title = {{Monotone (Nonlinear) Operators in Hilbert Space}},
     author = {George J. Minty},
    journal = {{Duke Mathematical Journal}},
        doi = {10.1215/s0012-7094-62-02933-2},
       year = {1962},
      month = {9},
  publisher = {{Duke University Press}},
     volume = {29},
     number = {3}
}
@article{friedlander2017efficient,
    title = {{Efficient Evaluation of Scaled Proximal Operators}},
   author = {Friedlander, Michael P and Goh, Gabriel},
  journal = {{Electronic Transactions on Numerical Analysis}},
   volume = {46},
    pages = {1--22},
     year = {2017}
}
@inproceedings{Gower2019_sgd,
      title = {{SGD: General Analysis and Improved Rates}},
     author = {Gower, Robert Mansel and Loizou, Nicolas and Qian, Xun and Sailanbayev, Alibek and Shulgin, Egor and Richt{\'a}rik, Peter},
  booktitle = {{Proceedings of the 36th International Conference on Machine Learning}},
      pages = {5200--5209},
       year = {2019},
     editor = {Chaudhuri, Kamalika and Salakhutdinov, Ruslan},
     volume = {97},
     series = {{Proceedings of Machine Learning Research}},
      month = {6},
  publisher = {{PMLR}}
}
@article{wang2019_pfl,
    title = {{Federated Evaluation of On-device Personalization}},
   author = {Kangkang Wang and Rajiv Mathews and Chloé Kiddon and Hubert Eichner and Françoise Beaufays and Daniel Ramage},
  journal = {{arXiv preprint arXiv:1910.10252v1}},
     year = {2019},
    month = {10},
      doi = {10.48550/ARXIV.1910.10252}
}
@article{Tang_2021_pfl_ecg,
      title = {{Personalized Federated Learning for ECG Classification Based on Feature Alignment}},
     author = {Renjie Tang and Junzhou Luo and Junbo Qian and Jiahui Jin},
    journal = {{Security and Communication Networks}},
        doi = {10.1155/2021/6217601},
       year = {2021},
      month = {11},
  publisher = {{Hindawi Limited}},
     volume = {2021},
      pages = {1--9},
     editor = {Lianbo Ma}
}
@article{Tan_2022_pfl,
      title = {{Towards Personalized Federated Learning}},
     author = {Alysa Ziying Tan and Han Yu and Lizhen Cui and Qiang Yang},
    journal = {{IEEE Transactions on Neural Networks and Learning Systems}},
        doi = {10.1109/tnnls.2022.3160699},
       year = {2022},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
      pages = {1--17}
}
@inproceedings{Kulkarni_2020_pfl,
      title = {{Survey of Personalization Techniques for Federated Learning}},
     author = {Viraj Kulkarni and Milind Kulkarni and Aniruddha Pant},
  booktitle = {{2020 Fourth World Conference on Smart Trends in Systems, Security and Sustainability (WorldS4)}},
        doi = {10.1109/worlds450073.2020.9210355},
       year = {2020},
      month = {7},
  publisher = {{IEEE}}
}
@article{arivazhagan2019_pfl_layer,
    title = {{Federated Learning with Personalization Layers}},
   author = {Manoj Ghuhan Arivazhagan and Vinay Aggarwal and Aaditya Kumar Singh and Sunav Choudhary},
  journal = {{arXiv preprint arXiv:1912.00818v1}},
      doi = {10.48550/ARXIV.1912.00818},
     year = {2019},
    month = {12}
}
@article{hard2018_fl_keyboard,
    title = {{Federated Learning for Mobile Keyboard Prediction}},
   author = {Andrew Hard and Kanishka Rao and Rajiv Mathews and Swaroop Ramaswamy and Françoise Beaufays and Sean Augenstein and Hubert Eichner and Chloé Kiddon and Daniel Ramage},
  journal = {{arXiv preprint arXiv:1811.03604v2}},
      doi = {10.48550/ARXIV.1811.03604},
     year = {2018},
    month = {11}
}
@article{yang2018_fl_google,
    title = {{Applied Federated Learning: Improving Google Keyboard Query Suggestions}},
   author = {Timothy Yang and Galen Andrew and Hubert Eichner and Haicheng Sun and Wei Li and Nicholas Kong and Daniel Ramage and Françoise Beaufays},
  journal = {{arXiv preprint arXiv:1812.02903v1}},
      doi = {10.48550/ARXIV.1812.02903},
     year = {2018},
    month = {12}
}
@inproceedings{Smith2017_fl_mtl,
      title = {{Federated Multi-Task Learning}},
     author = {Smith, Virginia and Chiang, Chao-Kai and Sanjabi, Maziar and Talwalkar, Ameet S},
  booktitle = {Advances in Neural Information Processing Systems},
     editor = {I. Guyon and U. Von Luxburg and S. Bengio and H. Wallach and R. Fergus and S. Vishwanathan and R. Garnett},
  publisher = {{Curran Associates, Inc.}},
     volume = {30},
       year = {2017}
}
@article{zhao2018_fl_noniid,
    title = {{Federated Learning with Non-IID Data}},
   author = {Yue Zhao and Meng Li and Liangzhen Lai and Naveen Suda and Damon Civin and Vikas Chandra},
  journal = {{arXiv preprint arXiv:1806.00582v2}},
      doi = {10.48550/ARXIV.1806.00582},
     year = {2018},
    month = {6}
}
@inproceedings{Sim_2019_pfl_audio,
      title = {{Personalization of End-to-End Speech Recognition on Mobile Devices for Named Entities}},
     author = {Khe Chai Sim and Francoise Beaufays and Arnaud Benard and Dhruv Guliani and Andreas Kabel and Nikhil Khare and Tamar Lucassen and Petr Zadrazil and Harry Zhang and Leif Johnson and Giovanni Motta and Lillian Zhou},
  booktitle = {{2019 IEEE Automatic Speech Recognition and Understanding Workshop (ASRU)}},
        doi = {10.1109/asru46091.2019.9003775},
       year = {2019},
      month = {12},
  publisher = {{IEEE}}
}
@article{chen2018_fml,
    title = {{Federated Meta-Learning with Fast Convergence and Efficient Communication}},
   author = {Fei Chen and Mi Luo and Zhenhua Dong and Zhenguo Li and Xiuqiang He},
  journal = {{arXiv preprint arXiv:1802.07876v2}},
      doi = {10.48550/ARXIV.1802.07876},
     year = {2018},
    month = {2}
}
@article{deng2020_apfl,
    title = {{Adaptive Personalized Federated Learning}},
   author = {Yuyang Deng and Mohammad Mahdi Kamani and Mehrdad Mahdavi},
  journal = {{arXiv preprint arXiv:2003.13461v3}},
      doi = {10.48550/ARXIV.2003.13461},
     year = {2020},
    month = {3}
}
@article{li2019_fedmd,
    title = {{FedMD: Heterogenous Federated Learning via Model Distillation}},
   author = {Daliang Li and Junpu Wang},
  journal = {{arXiv preprint arXiv:1910.03581v1}},
      doi = {10.48550/ARXIV.1910.03581},
     year = {2019},
    month = {10}
}
@article{shen2020_fml,
    title = {{Federated Mutual Learning}},
   author = {Tao Shen and Jie Zhang and Xinkang Jia and Fengda Zhang and Gang Huang and Pan Zhou and Kun Kuang and Fei Wu and Chao Wu},
  journal = {{arXiv preprint arXiv:2006.16765v3}},
      doi = {10.48550/ARXIV.2006.16765},
     year = {2020},
    month = {6}
}
@article{Strohmer_2008_Kaczmarz,
      title = {{A Randomized Kaczmarz Algorithm with Exponential Convergence}},
     author = {Thomas Strohmer and Roman Vershynin},
    journal = {{Journal of Fourier Analysis and Applications}},
        doi = {10.1007/s00041-008-9030-4},
       year = {2008},
      month = {4},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {15},
     number = {2},
      pages = {262--278}
}
@article{Needell_2015_Kaczmarz,
      title = {{Stochastic Gradient Descent, Weighted Sampling, and the Randomized Kaczmarz Algorithm}},
     author = {Deanna Needell and Nathan Srebro and Rachel Ward},
    journal = {{Mathematical Programming}},
        doi = {10.1007/s10107-015-0864-7},
       year = {2015},
      month = {2},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {155},
     number = {1-2},
      pages = {549--573}
}
@inproceedings{Zhao2015_sampling,
      title = {{Stochastic Optimization with Importance Sampling for Regularized Loss Minimization}},
     author = {Zhao, Peilin and Zhang, Tong},
  booktitle = {{Proceedings of the 32nd International Conference on Machine Learning}},
      pages = {1--9},
       year = {2015},
     editor = {Bach, Francis and Blei, David},
     volume = {37},
     series = {{Proceedings of Machine Learning Research}},
    address = {Lille, France},
      month = {7},
  publisher = {{PMLR}}
}
@inproceedings{Kovalev2020_loopless,
      title = {{Don’t Jump Through Hoops and Remove Those Loops:  SVRG and Katyusha are Better Without the Outer Loop}},
     author = {Kovalev, Dmitry and Horv{\'a}th, Samuel and Richt{\'a}rik, Peter},
  booktitle = {{Proceedings of the 31st International Conference  on Algorithmic Learning Theory}},
      pages = {451--467},
       year = {2020},
     editor = {Kontorovich, Aryeh and Neu, Gergely},
     volume = {117},
     series = {{Proceedings of Machine Learning Research}},
      month = {2},
  publisher = {{PMLR}}
}
@inproceedings{Liu_2020_Hierarchical,
      title = {{Client-Edge-Cloud Hierarchical Federated Learning}},
     author = {Lumin Liu and Jun Zhang and S.H. Song and Khaled B. Letaief},
  booktitle = {{ICC 2020 - 2020 IEEE International Conference on Communications}},
        doi = {10.1109/icc40277.2020.9148862},
       year = {2020},
      month = {6},
   location = {{Dublin, Ireland}},
  publisher = {{IEEE}}
}
@incollection{Malekmohammadi_2021_fl_os,
      title = {{Splitting Algorithms for Federated Learning}},
     author = {Saber Malekmohammadi and Kiarash Shaloudegi and Zeou Hu and Yaoliang Yu},
  booktitle = {{Communications in Computer and Information Science}},
        doi = {10.1007/978-3-030-93736-2_14},
       year = {2021},
  publisher = {{Springer International Publishing}},
      pages = {159--176}
}
@article{Han_2013_CDRSM,
      title = {{A Customized Douglas{\textendash}Rachford Splitting Algorithm for Separable Convex Minimization with Linear Constraints}},
     author = {Deren Han and Hongjin He and Hai Yang and Xiaoming Yuan},
    journal = {{Numerische Mathematik}},
        doi = {10.1007/s00211-013-0580-2},
       year = {2013},
      month = {9},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {127},
     number = {1},
      pages = {167--200}
}
@inproceedings{chen_2022_pfl_bench,
      title = {{pFL-Bench: A Comprehensive Benchmark for Personalized Federated Learning}},
     author = {Chen, Daoyuan and Gao, Dawei and Kuang, Weirui and Li, Yaliang and Ding, Bolin},
  booktitle = {{Advances in Neural Information Processing Systems}},
     editor = {S. Koyejo and S. Mohamed and A. Agarwal and D. Belgrave and K. Cho and A. Oh},
      pages = {9344--9360},
  publisher = {{Curran Associates, Inc.}},
     volume = {35},
       year = {2022}
}
@article{Xie_2023_fl_scope,
      title = {{FederatedScope: A Flexible Federated Learning Platform for Heterogeneity}},
     author = {Yuexiang Xie and Zhen Wang and Dawei Gao and Daoyuan Chen and Liuyi Yao and Weirui Kuang and Yaliang Li and Bolin Ding and Jingren Zhou},
    journal = {{Proceedings of the VLDB Endowment}},
        doi = {10.14778/3579075.3579081},
       year = {2023},
      month = {1},
  publisher = {{Association for Computing Machinery (ACM)}},
     volume = {16},
     number = {5},
      pages = {1059--1072}
}
@techreport{cifar,
        title = {{Learning Multiple Layers of Features from Tiny Images}},
       author = {Krizhevsky, Alex and Hinton, Geoffrey and others},
  institution = {{University of Toronto}},
         year = {2009}
}
@techreport{sent140,
        title = {{Twitter sentiment classification using distant supervision}},
       author = {Go, Alec and Bhayani, Richa and Huang, Lei},
  institution = {{Stanford University}},
         year = {2009}
}
@article{caldas2018_leaf,
    title = {{LEAF: A Benchmark for Federated Settings}},
   author = {Sebastian Caldas and Sai Meher Karthik Duddu and Peter Wu and Tian Li and Jakub Konečný and H. Brendan McMahan and Virginia Smith and Ameet Talwalkar},
  journal = {{arXiv preprint arXiv:1812.01097v3}},
      doi = {10.48550/ARXIV.1812.01097},
     year = {2018},
    month = {12}
}
@article{Li_2021_Blockchain_fl,
      title = {{Blockchain for Federated Learning toward Secure Distributed Machine Learning Systems: A Systemic Survey}},
     author = {Dun Li and Dezhi Han and Tien-Hsiung Weng and Zibin Zheng and Hongzhi Li and Han Liu and Arcangelo Castiglione and Kuan-Ching Li},
    journal = {{Soft Computing}},
        doi = {10.1007/s00500-021-06496-5},
       year = {2021},
      month = {11},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {26},
     number = {9},
      pages = {4423--4440}
}
@article{Wang_2022_Blockchain_fl,
      title = {{Blockchain Empowered Federated Learning for Data Sharing Incentive Mechanism}},
     author = {Zexin Wang and Biwei Yan and Anming Dong},
    journal = {{Procedia Computer Science}},
        doi = {10.1016/j.procs.2022.04.047},
       year = {2022},
  publisher = {{Elsevier BV}},
     volume = {202},
      pages = {348--353}
}
@book{Swan_2015_Blockchain,
      title = {{Blockchain: Blueprint for a New Economy}},
     author = {Swan, Melanie},
       year = {2015},
       isbn = {1491920491},
  publisher = {{O'Reilly Media, Inc.}},
    edition = {1st}
}
@incollection{Liu_2020_FedCoin,
      title = {{FedCoin: A Peer-to-Peer Payment System for Federated Learning}},
     author = {Yuan Liu and Zhengpeng Ai and Shuai Sun and Shuangfeng Zhang and Zelei Liu and Han Yu},
  booktitle = {{Lecture Notes in Computer Science}},
        doi = {10.1007/978-3-030-63076-8_9},
       year = {2020},
  publisher = {{Springer International Publishing}},
      pages = {125--138}
}
@article{Zhan_2020_incentive_fl,
      title = {{A Learning-Based Incentive Mechanism for Federated Learning}},
     author = {Yufeng Zhan and Peng Li and Zhihao Qu and Deze Zeng and Song Guo},
    journal = {{IEEE Internet of Things Journal}},
        doi = {10.1109/jiot.2020.2967772},
       year = {2020},
      month = {7},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {7},
     number = {7},
      pages = {6360--6368}
}
@article{Kang_2019_incentive_fl,
      title = {{Incentive Mechanism for Reliable Federated Learning: A Joint Optimization Approach to Combining Reputation and Contract Theory}},
     author = {Jiawen Kang and Zehui Xiong and Dusit Niyato and Shengli Xie and Junshan Zhang},
    journal = {{IEEE Internet of Things Journal}},
        doi = {10.1109/jiot.2019.2940820},
       year = {2019},
      month = {12},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {6},
     number = {6},
      pages = {10700--10714}
}
@article{Zhan_2021_incentive_fl,
      title = {{A Survey of Incentive Mechanism Design for Federated Learning}},
     author = {Yufeng Zhan and Jie Zhang and Zicong Hong and Leijie Wu and Peng Li and Song Guo},
    journal = {{IEEE Transactions on Emerging Topics in Computing}},
        doi = {10.1109/tetc.2021.3063517},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
       year = {2022},
     volume = {10},
     number = {2},
      pages = {1035-1044}
}
@article{Davis_2017_DYS,
      title = {{A Three-Operator Splitting Scheme and its Optimization Applications}},
     author = {Damek Davis and Wotao Yin},
    journal = {{Set-Valued and Variational Analysis}},
        doi = {10.1007/s11228-017-0421-z},
       year = {2017},
      month = {6},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {25},
     number = {4},
      pages = {829--858}
}
@article{Liu_2019_DYS,
      title = {{An Envelope for Davis{\textendash}Yin Splitting and Strict Saddle-Point Avoidance}},
     author = {Yanli Liu and Wotao Yin},
    journal = {{Journal of Optimization Theory and Applications}},
        doi = {10.1007/s10957-019-01477-z},
       year = {2019},
      month = {1},
  publisher = {{Springer Science and Business Media LLC}},
     volume = {181},
     number = {2},
      pages = {567--587}
}
@inproceedings{jupyter_2016,
      title = {Jupyter Notebooks - A Publishing Format for Reproducible Computational Workflows},
     author = {Thomas Kluyver and Benjamin Ragan-Kelley and Fernando P{\'e}rez and Brian Granger and Matthias Bussonnier and Jonathan Frederic and Kyle Kelley and Jessica Hamrick and Jason Grout and Sylvain Corlay and Paul Ivanov and Dami{\'a}n Avila and Safia Abdalla and Carol Willing and  Jupyter development team},
  booktitle = {Positioning and Power in Academic Publishing: Players, Agents and Agendas},
     editor = {Fernando Loizides and Birgit Scmidt},
  publisher = {{IOS Press}},
    address = {Netherlands},
       year = {2016},
      pages = {87--90}
}
@article{Granger_2021_Jupyter,
      title = {{Jupyter: Thinking and Storytelling With Code and Data}},
     author = {Brian E. Granger and Fernando Perez},
    journal = {{Computing in Science \& Engineering}},
        doi = {10.1109/mcse.2021.3059263},
       year = {2021},
      month = {3},
  publisher = {{Institute of Electrical and Electronics Engineers (IEEE)}},
     volume = {23},
     number = {2},
      pages = {7--14}
}
