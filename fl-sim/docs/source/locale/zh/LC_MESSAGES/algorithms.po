# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2023, W<PERSON> Hao
# This file is distributed under the same license as the fl-sim package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: fl-sim \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-01 02:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh\n"
"Language-Team: zh <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/algorithms.rst:8
msgid "Sections:"
msgstr ""

#: ../../source/algorithms.rst:2
msgid "Federated learning algorithms"
msgstr ""

#: ../../source/algorithms.rst:4
msgid ""
"Federated optimization algorithms have been the central problem in the "
"field of federated learning since its inception. This chapter contains a "
"collection of algorithms that have been proposed in the literature, which"
" will be analyzed from the viewpoint of optimization theory, espcially "
"the theory of operator splitting."
msgstr ""
