# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2023, W<PERSON> Hao
# This file is distributed under the same license as the fl-sim package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: fl-sim \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-01 02:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh\n"
"Language-Team: zh <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: fl_sim.optimizers:2 of
msgid "fl_sim.optimizers"
msgstr ""

#: fl_sim.optimizers:4 of
msgid ""
"This module contains the optimizers (local solvers) used in federated "
"learning. Despite optimizers from :mod:`torch.optim` and "
":mod:`torch_optimizer`, we also provide some custom optimizers for "
"federated learning for solving for example"
msgstr ""

#: fl_sim.optimizers:8 of
msgid "proximal optimization problem"
msgstr ""

#: fl_sim.optimizers:9 of
msgid "lagrangian dual problem"
msgstr ""

#: fl_sim.optimizers:23:<autosummary>:1 of
msgid ""
":py:obj:`get_optimizer <fl_sim.optimizers.get_optimizer>`\\ "
"\\(optimizer\\_name\\, params\\, config\\)"
msgstr ""

#: fl_sim.optimizers:23:<autosummary>:1 of
msgid "Get optimizer by name."
msgstr ""

#: fl_sim.optimizers:23:<autosummary>:1 of
msgid ""
":py:obj:`register_optimizer <fl_sim.optimizers.register_optimizer>`\\ "
"\\(\\[name\\, override\\]\\)"
msgstr ""

#: fl_sim.optimizers:23:<autosummary>:1 of
msgid "Decorator to register a new optimizer."
msgstr ""
