# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2023, W<PERSON> Hao
# This file is distributed under the same license as the fl-sim package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: fl-sim \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-01 02:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh\n"
"Language-Team: zh <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/algorithms/primal_dual.rst:4
msgid "Primal-Dual Algorithms in Federated Learning"
msgstr ""

#: ../../source/algorithms/primal_dual.rst:6
msgid ""
"In traditional optimization methods, the primal-dual algorithm is a kind "
"of frequently used algorithm that solves the primal and dual problems."
msgstr ""

#: ../../source/algorithms/primal_dual.rst:9
msgid "to write more...."
msgstr ""
