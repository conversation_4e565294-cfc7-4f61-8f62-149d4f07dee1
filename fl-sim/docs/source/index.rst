.. fl-sim documentation master file, created by
   sphinx-quickstart on Tue Jul 18 22:53:23 2023.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to fl-sim's documentation!
==================================

A Simple Simulation Framework for Federated Learning Based on PyTorch.

Project Links: `GitHub <https://github.com/wenh06/fl-sim>`_ | `gitee <https://gitee.com/wenh06/fl-sim/>`_

.. toctree::
   :caption: Getting started
   :maxdepth: 1

   install
   examples

.. toctree::
   :caption: API Reference
   :maxdepth: 1

   api/nodes
   api/datasets
   api/models
   api/optimizers
   api/regularizers
   api/utils

.. toctree::
   :caption: Advanced Topics
   :maxdepth: 1

   algorithms
   cli
   viz

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
