Federated learning algorithms
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Federated optimization algorithms have been the central problem in the field of federated learning since its inception.
This chapter contains a collection of algorithms that have been proposed in the literature, which will be analyzed
from the viewpoint of optimization theory, espcially the theory of operator splitting.

.. toctree::
   :maxdepth: 1
   :caption: Sections:

   algorithms/overview
   algorithms/proximal
   algorithms/primal_dual
   algorithms/operator_splitting
   algorithms/skipping
