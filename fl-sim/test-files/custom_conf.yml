# This is a custom configuration file for testing.
# NOTE: the command `fl-sim test-files/custom_conf.yml` should be run in the root directory of fl-sim.

algorithm:
  name: test-files/custom_alg.Custom
  server:
    num_clients: null
    clients_sample_ratio: 0.1
    num_iters: 2  # only for a quick test in GitHub Actions, normally much larger
    vr: true
    log_dir: action-test
    tag: sample_ratio_${{ algorithm.server.clients_sample_ratio }}-seed_${{ seed }}
  client:
    lr: 0.03
    num_epochs: 10
    batch_size: null  # null for default batch size
    vr: ${{ algorithm.server.vr }}
    scheduler:
      name: step  # StepLR
      step_size: 1
      gamma: 0.99
dataset:
  name: test-files/custom_dataset.CustomFEMNIST
  datadir: null  # default dir
  transform: none  # none for static transform (only normalization, no augmentation)
model:
  name: cnn_femmist_tiny
seed: 0
