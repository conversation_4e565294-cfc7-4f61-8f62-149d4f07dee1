# .readthedocs.yaml
# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

# Set the version of Python and other tools you might need
build:
  os: ubuntu-22.04  # ubuntu-lts-latest
  tools:
    python: "3.9"
  apt_packages:
    - texlive-full
    - build-essential
    - ffmpeg
    - libsm6
    - libxext6
    - libsndfile1
    - latexmk
    - pandoc
    - pdf2svg
  jobs:
    pre_build:
      - python docs/pre_build.py

# Build documentation in the docs/ directory with Sphinx
sphinx:
  builder: html
  configuration: docs/source/conf.py

# If using Sphinx, optionally build your docs in additional formats such as PDF
# formats:
#   - pdf
#   - epub

# Optionally declare the Python requirements required to build your docs
python:
  install:
  - requirements: requirements.txt
  - requirements: docs/requirements.txt
  - method: pip
    path: .
    extra_requirements:
      - docs
