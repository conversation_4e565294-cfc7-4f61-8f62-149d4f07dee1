"""
Functions for collecting experiment results, visualizing them, etc.
"""

import itertools
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Sequence, Tuple, Union

import matplotlib as mpl
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import yaml
from termcolor import colored
from torch_ecg.utils import MovingAverage

try:
    import ipywidgets as widgets
    from IPython.display import display
except (ImportError, ModuleNotFoundError):
    widgets = display = None

from ..nodes import Node
from .const import LOG_DIR
from .misc import find_longest_common_substring, is_notebook

__all__ = [
    "find_log_files",
    "get_config_from_log",
    "aggregate_results_from_json_log_with_custom_axis",
    "get_curves_and_labels_from_log",
    "plot_curves",
    "plot_mean_curve_with_error_bounds",
    "Panel",
]


# turn off the progress bar for loading log files
os.environ["FLSIM_VERBOSE"] = "0"


_linestyle_tuple = [  # name, linestyle (offset, on-off-seq)
    ("solid", (0, ())),
    ("densely dashed", (0, (5, 1))),
    ("densely dotted", (0, (1, 1))),
    ("densely dashdotted", (0, (3, 1, 1, 1))),
    ("densely dashdotdotted", (0, (3, 1, 1, 1, 1, 1))),
    ("dashed", (0, (5, 5))),
    ("dotted", (0, (1, 1))),
    ("dashdotdotted", (0, (3, 5, 1, 5, 1, 5))),
    ("dashdotted", (0, (3, 5, 1, 5))),
    ("loosely dashdotdotted", (0, (3, 10, 1, 10, 1, 10))),
    ("loosely dashdotted", (0, (3, 10, 1, 10))),
    ("loosely dotted", (0, (1, 10))),
    ("long dash with offset", (5, (10, 3))),
    ("loosely dashed", (0, (5, 10))),
]
# _linestyle_cycle = itertools.cycle([ls for _, ls in _linestyle_tuple])
_marker_cycle = ("o", "s", "v", "^", "<", ">", "p", "P", "*")

# fmt: off
_color_palettes = [
    # seaborn color palettes
    "deep", "muted", "bright", "pastel", "dark", "colorblind",
    # matplotlib color palettes
    "tab10", "tab20", "tab20b", "tab20c",
    "Pastel1", "Pastel2", "Paired", "Accent", "Dark2",
    "Set1", "Set2", "Set3",
]
# fmt: on
sns.set_palette("tab10")

DEFAULT_RC_PARAMS = {
    "xtick.labelsize": 18,
    "ytick.labelsize": 18,
    "axes.labelsize": 22,
    "legend.fontsize": 18,
    "legend.title_fontsize": 20,
    "figure.figsize": [16, 8],
    "lines.linewidth": 2.6,
    "font.family": ["sans-serif"],
}
if mpl is not None:
    # NOTE: to use Windows fonts on a Linux machine (e.g. Ubuntu),
    # one can execute the following commands:
    # sudo apt install ttf-mscorefonts-installer
    # sudo fc-cache -fv
    font_files = mpl.font_manager.findSystemFonts()
    for font_file in font_files:
        try:
            mpl.font_manager.fontManager.addfont(font_file)
        except Exception:
            pass
    _font_names = [item.name for item in mpl.font_manager.fontManager.ttflist]
    _fonts_priority = [
        "Helvetica",  # recommended by science (https://www.science.org/content/page/instructions-preparing-initial-manuscript)
        "Arial",  # alternative to Helvetica for Windows users
        "TeX Gyre Heros",  # alternative to Helvetica for Linux users
        "Roboto",  # alternative to Helvetica for Android users
        "Arimo",  # alternative to Helvetica, cross-platform
        "Nimbus Sans",  # alternative to Helvetica
        "CMU Serif",  # Computer Modern Roman, default for LaTeX, serif font
        "JDLangZhengTi",  # sans-serif font for Chinese
        "Times New Roman",  # less recommended, serif font
        "DejaVu Sans",  # default sans-serif font for matplotlib
    ]
    _fonts_priority = [item for item in _fonts_priority if item in _font_names]
    if len(_fonts_priority) == 0:
        _fonts_priority = ["sans-serif"]
    for font in _fonts_priority:
        if font in _font_names:
            DEFAULT_RC_PARAMS["font.family"] = [font]
            break
    print(f"FL-SIM Panel using default font {DEFAULT_RC_PARAMS['font.family']}")
    mpl.rcParams.update(DEFAULT_RC_PARAMS)
    plt.rcParams.update(DEFAULT_RC_PARAMS)
else:
    _font_names, _fonts_priority = None, None


def find_log_files(directory: Union[str, Path] = LOG_DIR, filters: str = "", show: bool = False) -> Union[List[Path], None]:
    """Find log files in the given directory, recursively.

    Parameters
    ----------
    directory : Union[str, pathlib.Path], default fl_sim.utils.const.LOG_DIR
        The directory to search for log files.
    filters : str, default ""
        Filters for the log files.
        Only files fitting the pattern of `filters` will be returned.
    show : bool, default False
        Whether to print the found log files.
        If True, the found log files will be printed and **NOT** returned.

    Returns
    -------
    List[pathlib.Path]
        The list of log files.

    """
    log_files = [item for item in Path(directory).rglob("*.json") if item.is_file() and re.search(filters, item.name)]
    if show:
        for idx, fn in enumerate(log_files):
            print(idx, "---", fn.stem)
    else:
        return sorted(log_files)


def get_config_from_log(file: Union[str, Path]) -> dict:
    """Get the config from the log file.

    Parameters
    ----------
    file : Union[str, pathlib.Path]
        Path to the log file.

    Returns
    -------
    dict
        The config.

    """
    file = Path(file)
    if not file.exists():
        print(colored("File not found", "red"))
        return {}
    if file.suffix == ".json":
        file = file.with_suffix(".txt")
    if not file.exists():
        print(colored("Corresponding text log file not found", "red"))
        return {}
    contents = file.read_text().splitlines()
    flag = False
    for idx, line in enumerate(contents):
        if "FLSim - INFO - Experiment config:" in line:
            flag = True
            break
    if flag:
        return eval(contents[idx + 1])
    else:
        print("Config not found")
        return {}


def aggregate_results_from_json_log_with_custom_axis(
    d: Union[dict, str, Path],
    part: str = "val",
    x_metric: str = "step",
    y_metric: str = "acc"
) -> Dict[str, np.ndarray]:
    """Aggregate the federated results from json log with custom x and y axis.

    Parameters
    ----------
    d : dict or str or pathlib.Path
        The dict of the json/yaml log, or the path to the json/yaml log file.
    part : str, default "val"
        The part of the log to aggregate.
    x_metric : str, default "step"
        The metric to use for x-axis.
    y_metric : str, default "acc"
        The metric to use for y-axis.

    Returns
    -------
    Dict[str, numpy.ndarray]
        Dictionary containing 'x' and 'y' arrays for plotting.

    """
    if isinstance(d, (str, Path)):
        d = Path(d)
        if d.suffix == ".json":
            import json
            d = json.loads(d.read_text())
        elif d.suffix in [".yaml", ".yml"]:
            import yaml
            d = yaml.safe_load(d.read_text())
        else:
            raise ValueError(f"unsupported file type: {d.suffix}")

    if part == "val":
        # For validation data, use server data directly
        if "Server" in d[part]:
            server_data = d[part]["Server"]
            x_values = [item[x_metric] for item in server_data]
            y_values = [item[y_metric] for item in server_data]

            # Sort by x_metric values
            sorted_pairs = sorted(zip(x_values, y_values), key=lambda pair: pair[0])
            x_sorted, y_sorted = zip(*sorted_pairs) if sorted_pairs else ([], [])

            return {"x": np.array(x_sorted), "y": np.array(y_sorted)}
        else:
            return {"x": np.array([]), "y": np.array([])}

    else:
        # For training data, aggregate client data (excluding server)
        from tqdm import tqdm
        import os

        # Get all unique x_metric values across all clients (excluding server)
        all_x_values = []
        for client_name, client_data in d[part].items():
            if client_name == "Server":  # Exact match for "Server"
                continue
            for item in client_data:
                all_x_values.append(item[x_metric])

        if not all_x_values:
            return {"x": np.array([]), "y": np.array([])}

        unique_x_values = sorted(set(all_x_values))

        # Aggregate y_metric values for each x_metric value
        aggregated_y = []
        for x_val in unique_x_values:
            weighted_sum = 0
            total_samples = 0

            for client_name, client_data in tqdm(
                d[part].items(),
                mininterval=1,
                desc="Aggregating results",
                total=len(d[part]),
                unit="client",
                leave=False,
                disable=int(os.environ.get("FLSIM_VERBOSE", "1")) < 1,
            ):
                if client_name == "Server":  # Exact match for "Server"
                    continue

                for item in client_data:
                    if item[x_metric] == x_val:
                        weighted_sum += item[y_metric] * item["num_samples"]
                        total_samples += item["num_samples"]

            if total_samples > 0:
                aggregated_y.append(weighted_sum / total_samples)
            else:
                aggregated_y.append(0.0)

        return {"x": np.array(unique_x_values), "y": np.array(aggregated_y)}


def get_curves_and_labels_from_log(
    files: Union[str, Path, Sequence[Union[str, Path]]],
    part: str = "val",
    x_metric: str = "step",
    y_metric: str = "acc",
) -> Tuple[List[Dict[str, np.ndarray]], List[str]]:
    """Get the curves and labels (stems) from the given log file(s).

    Parameters
    ----------
    files : Union[str, pathlib.Path, Sequence[Union[str, pathlib.Path]]]
        The log file(s).
    part : str, default "val"
        The part of the data, e.g., "train", "val", "test", etc.
    x_metric : str, default "step"
        The metric to use for x-axis, e.g., "step", "epoch", "comm", etc.
    y_metric : str, default "acc"
        The metric to use for y-axis, e.g., "acc", "top3_acc", "loss", etc.

    Returns
    -------
    Tuple[List[Dict[str, numpy.ndarray]], List[str]]
        The curves (containing x and y data) and labels.

    """
    curves = []
    stems = []
    if isinstance(files, (str, Path)):
        files = [files]
    for file in files:
        curve_data = aggregate_results_from_json_log_with_custom_axis(
            file,
            part=part,
            x_metric=x_metric,
            y_metric=y_metric,
        )
        curves.append(curve_data)
        stems.append(Path(file).stem)
    return curves, stems


def _plot_curves(
    curves: Sequence[Union[np.ndarray, Dict[str, np.ndarray]]],
    labels: Sequence[str],
    fig_ax: Optional[Tuple[plt.Figure, plt.Axes]] = None,
    markers: bool = True,
    x_range: Optional[Tuple[float, float]] = None,
    y_range: Optional[Tuple[float, float]] = None,
    x_label: str = "Global Iter.",
) -> Tuple[plt.Figure, plt.Axes]:
    """Plot the curves with support for custom x-axis data.

    Parameters
    ----------
    curves : Sequence[Union[numpy.ndarray, Dict[str, numpy.ndarray]]]
        The curves. Can be either numpy arrays (legacy format) or dictionaries
        containing 'x' and 'y' arrays for custom axis plotting.
    labels : Sequence[str]
        The labels for each curve.
    fig_ax : Optional[Tuple[plt.Figure, plt.Axes]], default None
        The figure and axes to plot on. If None, creates new figure.
    markers : bool, default True
        Whether to use markers on the plot.
    x_range : Optional[Tuple[float, float]], default None
        The range of x-axis values to display.
    y_range : Optional[Tuple[float, float]], default None
        The range of y-axis values to display.
    x_label : str, default "Global Iter."
        The label for the x-axis.

    Returns
    -------
    Tuple[plt.Figure, plt.Axes]
        The figure and axes objects.

    """
    if fig_ax is None:
        fig, ax = plt.subplots()
    else:
        fig, ax = fig_ax

    linestyle_cycle = itertools.cycle([ls for _, ls in _linestyle_tuple])
    marker_cycle = itertools.cycle(_marker_cycle)

    for idx, curve in enumerate(curves):
        plot_config = dict()
        if markers:
            plot_config["marker"] = next(marker_cycle)
        plot_config["linestyle"] = next(linestyle_cycle)
        plot_config["label"] = labels[idx]

        # Handle both legacy format (numpy array) and new format (dict with x, y)
        if isinstance(curve, dict) and "x" in curve and "y" in curve:
            plot_x = curve["x"]
            plot_y = curve["y"]
        else:
            # Legacy format: assume curve is y-values with sequential x-values
            plot_y = curve
            plot_x = np.arange(len(curve))

        # Apply x_range filtering if specified
        if x_range is not None:
            x_min, x_max = x_range
            if x_min is None:
                x_min = -np.inf
            if x_max is None:
                x_max = np.inf

            # Filter data points within x_range
            mask = (plot_x >= x_min) & (plot_x <= x_max)
            plot_x = plot_x[mask]
            plot_y = plot_y[mask]

        # Apply y_range filtering if specified
        if y_range is not None:
            y_min, y_max = y_range
            if y_min is None:
                y_min = -np.inf
            if y_max is None:
                y_max = np.inf
            plot_y = np.where((plot_y < y_min) | (plot_y > y_max), np.nan, plot_y)

        ax.plot(plot_x, plot_y, **plot_config)

    ax.legend(loc="best")
    ax.set_xlabel(x_label)
    return fig, ax


def plot_curves(
    files: Union[str, Path, Sequence[Union[str, Path]]],
    part: str = "val",
    x_metric: str = "step",
    y_metric: str = "acc",
    fig_ax: Optional[Tuple[plt.Figure, plt.Axes]] = None,
    labels: Union[str, Sequence[str]] = None,
) -> Tuple[plt.Figure, plt.Axes]:
    """Plot the curve of the given part and metrics from the given log file(s).

    Parameters
    ----------
    files : Union[str, pathlib.Path, Sequence[Union[str, pathlib.Path]]]
        The log file(s).
    part : str, default "val"
        The part of the data, e.g., "train", "val", "test", etc.
    x_metric : str, default "step"
        The metric to use for x-axis, e.g., "step", "epoch", "comm", etc.
    y_metric : str, default "acc"
        The metric to use for y-axis, e.g., "acc", "top3_acc", "loss", etc.
    fig_ax : Optional[Tuple[plt.Figure, plt.Axes]], default None
        The figure and axes to plot on.
        If None, a new figure and axes will be created.
    labels : Union[str, Sequence[str]], default None
        The labels for the curves.
        If None, the stem of the log file(s) will be used.

    Returns
    -------
    Tuple[plt.Figure, plt.Axes]
        The figure and axes.

    """
    curves, stems = get_curves_and_labels_from_log(files, part=part, x_metric=x_metric, y_metric=y_metric)
    if labels is None:
        labels = stems
    fig, ax = _plot_curves(curves, labels, fig_ax, x_label=x_metric)
    ax.set_ylabel(f"{part} {y_metric}")
    return fig, ax


def plot_mean_curve_with_error_bounds(
    curves: Sequence[Union[np.ndarray, Dict[str, np.ndarray]]],
    error_type: str = "std",
    fig_ax: Optional[Tuple[plt.Figure, plt.Axes]] = None,
    label: Optional[str] = None,
    show_error_bounds: bool = True,
    error_bound_label: bool = True,
    plot_config: Optional[Dict[str, Any]] = None,
    fill_between_config: Dict[str, Any] = {"alpha": 0.3},
    x_range: Optional[Tuple[float, float]] = None,
    y_range: Optional[Tuple[float, float]] = None,
    x_label: str = "Global Iter.",
) -> Tuple[plt.Figure, plt.Axes]:
    """Plot the mean curve with error bounds, supporting custom x-axis data.

    Parameters
    ----------
    curves : Sequence[Union[numpy.ndarray, Dict[str, numpy.ndarray]]]
        The curves. Can be either numpy arrays (legacy format) or dictionaries
        containing 'x' and 'y' arrays for custom axis plotting.
    error_type : {"std", "sem", "quartile", "iqr"}, default "std"
        The type of error bounds. Can be one of
            - "std": standard deviation
            - "sem": standard error of the mean
            - "quartile": quartile
            - "iqr": interquartile range
    fig_ax : Optional[Tuple[plt.Figure, plt.Axes]], optional
        The figure and axes to plot on.
        If None, a new figure and axes will be created.
    label : Optional[str], optional
        The label for the mean curve.
        Default to ``"mean"``.
    show_error_bounds : bool, default True
        Whether to show the error bounds.
    error_bound_label : bool, default True
        Whether to add the label for the error bounds.
    plot_config : Optional[Dict[str, Any]], optional
        The plot config for the mean curve passed to ``ax.plot``.
    fill_between_config : Dict[str, Any], default {"alpha": 0.3}
        The config for ``ax.fill_between``.
    x_range : Optional[Tuple[float, float]], optional
        The range of x-axis. Values outside the range will be discarded.
    y_range : Optional[Tuple[float, float]], optional
        The range of y-axis. Values outside the range will be set to NaN.
    x_label : str, default "Global Iter."
        The label for the x-axis.

    Returns
    -------
    Tuple[plt.Figure, plt.Axes]
        The figure and axes.

    """
    if fig_ax is None:
        fig, ax = plt.subplots()
    else:
        fig, ax = fig_ax

    # Extract x and y data from curves, handling both legacy and new formats
    x_data_list = []
    y_data_list = []

    for curve in curves:
        if isinstance(curve, dict) and "x" in curve and "y" in curve:
            x_vals = curve["x"]
            y_vals = curve["y"]
        else:
            # Legacy format: assume curve is y-values with sequential x-values
            y_vals = curve
            x_vals = np.arange(len(curve))

        x_data_list.append(x_vals)
        y_data_list.append(y_vals)

    # Find common x-axis values across all curves
    all_x_values = set()
    for x_vals in x_data_list:
        all_x_values.update(x_vals)

    common_x = np.array(sorted(all_x_values))

    # Apply x_range filtering if specified
    if x_range is not None:
        x_min, x_max = x_range
        if x_min is None:
            x_min = -np.inf
        if x_max is None:
            x_max = np.inf
        mask = (common_x >= x_min) & (common_x <= x_max)
        common_x = common_x[mask]

    # Interpolate all curves to common x-axis
    interpolated_curves = []
    for x_vals, y_vals in zip(x_data_list, y_data_list):
        # Create interpolated y values for common x points
        interpolated_y = np.full(len(common_x), np.nan)

        for i, x_target in enumerate(common_x):
            # Find exact matches first
            exact_matches = np.where(x_vals == x_target)[0]
            if len(exact_matches) > 0:
                interpolated_y[i] = y_vals[exact_matches[0]]

        interpolated_curves.append(interpolated_y)

    # Convert to numpy array for easier computation
    curves_array = np.array(interpolated_curves)

    # Apply y_range filtering if specified
    if y_range is not None:
        y_min, y_max = y_range
        if y_min is None:
            y_min = -np.inf
        if y_max is None:
            y_max = np.inf
        curves_array = np.where((curves_array < y_min) | (curves_array > y_max), np.nan, curves_array)

    # Calculate mean curve
    mean_curve = np.nanmean(curves_array, axis=0)

    # Plot mean curve
    ax.plot(common_x, mean_curve, label=label or "mean", **(plot_config or {}))

    # Plot error bounds if requested
    if show_error_bounds:
        if error_type == "std":
            std_curve = np.nanstd(curves_array, axis=0)
            upper_curve = mean_curve + std_curve
            lower_curve = mean_curve - std_curve
        elif error_type == "sem":
            std_curve = np.nanstd(curves_array, axis=0)
            n_valid = np.sum(~np.isnan(curves_array), axis=0)
            upper_curve = mean_curve + std_curve / np.sqrt(n_valid)
            lower_curve = mean_curve - std_curve / np.sqrt(n_valid)
        elif error_type == "quartile":
            q3 = np.nanquantile(curves_array, 0.75, axis=0)
            q1 = np.nanquantile(curves_array, 0.25, axis=0)
            upper_curve = q3
            lower_curve = q1
        elif error_type == "iqr":
            q3 = np.nanquantile(curves_array, 0.75, axis=0)
            q1 = np.nanquantile(curves_array, 0.25, axis=0)
            iqr = q3 - q1
            upper_curve = q3 + 1.5 * iqr
            lower_curve = q1 - 1.5 * iqr
        else:
            raise ValueError(f"Unknown error type: {error_type}")

        if error_bound_label:
            _error_type = {
                "std": "STD",
                "sem": "SEM",
                "quartile": "Quartile",
                "iqr": "IQR",
            }[error_type]
            fill_between_config["label"] = error_type if label is None else f"{label}±{_error_type}"

        ax.fill_between(
            common_x,
            lower_curve,
            upper_curve,
            **fill_between_config,
        )

    ax.legend(loc="best")
    ax.set_xlabel(x_label)
    return fig, ax


class Panel:
    """Panel for visualizing experiment results.

    Parameters
    ----------
    logdir : Optional[Union[str, pathlib.Path]], optional
        The directory to search for log files.
        Defaults to `fl_sim.utils.const.LOG_DIR`.

    TODO
    ----
    1. ~~add sliders for matplotlib rc params~~(done)
    2. ~~add a input box and a button for saving the figure~~(done)
    3. ~~add a box for showing the config of the experiment~~(done)
    4. ~~use `ToggleButtons` or `TagsInput` to specify indicators for merging multiple curves~~(done)
    5. ~~add choices (via `Dropdown`) for color palette~~(done)
    6. ~~add a dropdown selector for the sub-directories of the log directory~~(done)
    7. load all the log files in background into a file-level cache buffer
    8. use tabs to separate and add more configurations

    """

    __name__ = "Panel"

    __default_rc_params__ = DEFAULT_RC_PARAMS.copy()

    def __init__(
        self,
        logdir: Optional[Union[str, Path]] = None,
        rc_params: Optional[dict] = None,
        debug: bool = False,
    ) -> None:
        if widgets is None:
            print("One or more of the required packages is not installed: " "ipywidgets, matplotlib.")
            return
        self._is_notebook = is_notebook()
        if not self._is_notebook:
            print("Panel is only supported in Jupyter Notebook (JupyterLab, Colab, SageMaker, etc.).")
            return
        self._logdir = Path(logdir or LOG_DIR).expanduser().resolve()
        assert self._logdir.exists(), f"Log directory {self._logdir} does not exist."
        self._rc_params = self.__default_rc_params__.copy()
        self._rc_params.update(rc_params or {})
        assert set(self._rc_params.keys()).issubset(
            set(mpl.rcParams)
        ), f"Invalid rc_params: {set(self._rc_params) - set(mpl.rcParams)}."
        self.reset_matplotlib()
        sns.set()
        self.reset_matplotlib(rc_params=self._rc_params)
        self._debug = debug
        self._debug_log_sep = "-" * 80

        self._curve_cache = {}

        self._log_files = find_log_files(directory=self._logdir)
        self._subdir_dropdown_selector = widgets.Dropdown(
            options=["./"] + [d.name for d in self._logdir.iterdir() if d.is_dir() and len(list(d.glob("*.json"))) > 0],
            value="./",
            description="Sub-directory:",
            disabled=False,
            style={"description_width": "initial"},
        )
        self._subdir_dropdown_selector.observe(self._on_subdir_dropdown_change, names="value")
        self._subdir_refresh_button = widgets.Button(
            description="Refresh",
            disabled=False,
            button_style="",  # 'primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Refresh",
            icon="refresh",  # (FontAwesome names without the `fa-` prefix)
        )
        self._subdir_refresh_button.on_click(self._on_subdir_refresh_button_clicked)

        self._files_refresh_button = widgets.Button(
            description="Refresh",
            disabled=False,
            button_style="",  # primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Refresh",
            icon="refresh",  # (FontAwesome names without the `fa-` prefix)
        )
        self._filters_input = widgets.Text(
            value="",
            placeholder="",
            description="File filters:",
            disabled=False,
            layout={"width": "300px"},
        )
        self._log_files_mult_selector = widgets.SelectMultiple(
            options=list(zip(self.log_files, self._log_files)),
            description="Select log files:",
            disabled=False,
            layout={"width": "800px", "height": "220px"},
            style={"description_width": "initial"},
        )
        unit = "files" if len(self._log_files) > 1 else "file"
        unit_selected = "files" if len(self._log_files_mult_selector.value) > 1 else "file"
        self._num_log_files_label = widgets.Label(
            value=(
                f"Found {len(self.log_files)} log {unit}. "
                f"Selected {len(self._log_files_mult_selector.value)} log {unit_selected}."
            )
        )
        # clear self._fig_curves, self._fig_stems if selected log files change
        self._log_files_mult_selector.observe(self._log_files_mult_selector_changed, names="value")
        self._files_refresh_button.on_click(self._on_files_refresh_button_clicked)

        self._fig_curves, self._fig_stems = None, None
        fig_setup_slider_config = dict(
            step=1,
            disabled=False,
            continuous_update=False,
            orientation="horizontal",
            readout=True,
            readout_format="d",
            style={"description_width": "initial"},
        )
        init_fig_width, init_fig_height = self._rc_params["figure.figsize"]
        init_x_ticks_font_size = self._rc_params["xtick.labelsize"]
        init_y_ticks_font_size = self._rc_params["ytick.labelsize"]
        init_axes_label_font_size = self._rc_params["axes.labelsize"]
        init_legend_font_size = self._rc_params["legend.fontsize"]
        init_linewidth = self._rc_params["lines.linewidth"]
        self._fig_width_slider = widgets.IntSlider(
            value=int(init_fig_width),
            min=6,
            max=20,
            description="Fig. width:",
            **fig_setup_slider_config,
        )
        self._fig_width_slider.observe(self._on_fig_width_slider_value_changed, names="value")
        self._fig_height_slider = widgets.IntSlider(
            value=int(init_fig_height),
            min=3,
            max=12,
            description="Fig. height:",
            **fig_setup_slider_config,
        )
        self._fig_height_slider.observe(self._on_fig_height_slider_value_changed, names="value")
        self._x_ticks_font_size_slider = widgets.IntSlider(
            value=int(init_x_ticks_font_size),
            min=6,
            max=32,
            description="X tick font size:",
            **fig_setup_slider_config,
        )
        self._x_ticks_font_size_slider.observe(self._on_x_ticks_font_size_slider_value_changed, names="value")
        self._y_ticks_font_size_slider = widgets.IntSlider(
            value=int(init_y_ticks_font_size),
            min=6,
            max=32,
            description="Y tick font size:",
            **fig_setup_slider_config,
        )
        self._y_ticks_font_size_slider.observe(self._on_y_ticks_font_size_slider_value_changed, names="value")
        self._axes_label_font_size_slider = widgets.IntSlider(
            value=int(init_axes_label_font_size),
            min=6,
            max=42,
            description="Axes label font size:",
            **fig_setup_slider_config,
        )
        self._axes_label_font_size_slider.observe(self._on_axes_label_font_size_slider_value_changed, names="value")
        self._legend_font_size_slider = widgets.IntSlider(
            value=int(init_legend_font_size),
            min=6,
            max=32,
            description="Legend font size:",
            **fig_setup_slider_config,
        )
        self._legend_font_size_slider.observe(self._on_legend_font_size_slider_value_changed, names="value")
        self._linewidth_slider = widgets.FloatSlider(
            value=init_linewidth,
            min=0.6,
            max=4.4,
            description="Line width:",
            **{**fig_setup_slider_config, **{"step": 0.1, "readout_format": ".1f"}},
        )
        self._linewidth_slider.observe(self._on_linewidth_slider_value_changed, names="value")
        self._fill_between_alpha_slider = widgets.FloatSlider(
            value=0.3,
            min=0.1,
            max=0.9,
            description="Fill between alpha:",
            **{**fig_setup_slider_config, **{"step": 0.01, "readout_format": ".2f"}},
        )
        self._fill_between_alpha_slider.observe(self._on_fill_between_alpha_slider_value_changed, names="value")
        self._moving_averager = MovingAverage()
        self._moving_average_slider = widgets.FloatSlider(
            value=0.0,
            min=0.0,
            max=0.9,
            description="Curve smoothing:",
            **{**fig_setup_slider_config, **{"step": 0.01, "readout_format": ".2f"}},
        )
        self._moving_average_slider.observe(self._on_moving_average_slider_value_changed, names="value")

        slider_box = widgets.GridBox(
            [
                self._fig_width_slider,
                self._fig_height_slider,
                self._linewidth_slider,
                self._x_ticks_font_size_slider,
                self._y_ticks_font_size_slider,
                self._axes_label_font_size_slider,
                self._legend_font_size_slider,
                self._fill_between_alpha_slider,
                self._moving_average_slider,
            ],
            layout=widgets.Layout(
                grid_template_columns="repeat(3, 0.5fr)",
                grid_template_rows="repeat(3, 0.5fr)",
                grid_gap="0px 0px",
            ),
        )

        self._part_input = widgets.Text(
            value="val",
            placeholder="val/train/...",
            description="Part:",
            disabled=False,
            layout={"width": "150px"},
        )
        self._x_metric_input = widgets.Text(
            value="step",
            placeholder="step/epoch/comm/...",
            description="X-axis metric:",
            disabled=False,
            layout={"width": "150px"},
            style={"description_width": "initial"},
        )
        self._y_metric_input = widgets.Text(
            value="acc",
            placeholder="acc/loss/...",
            description="Y-axis metric:",
            disabled=False,
            layout={"width": "150px"},
            style={"description_width": "initial"},
        )
        self._refresh_part_metric_button = widgets.Button(
            description="Refresh metrics/ylabel",
            disabled=False,
            button_style="warning",  # primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Refresh part and metrics",
            icon="refresh",  # (FontAwesome names without the `fa-` prefix)
            layout={"width": "180px"},
        )
        self._xlabel_input = widgets.Text(
            value="",
            placeholder="Global Iterations/Epochs/Communications/...",
            description="X label:",
            style={"description_width": "initial"},
        )
        self._ylabel_input = widgets.Text(
            value="",
            placeholder="Test/Train/Val Accuracy/Loss/...",
            description="Y label:",
            style={"description_width": "initial"},
        )
        self._refresh_part_metric_button.on_click(self._on_refresh_part_metric_button_clicked)

        self._merge_curve_method_dropdown_selector = widgets.Dropdown(
            options=[
                ("standard deviation", "std"),
                ("standard error of the mean", "sem"),
                ("quartile", "quartile"),
                ("interquartile range", "iqr"),
            ],
            value="std",
            description="Merge error bound type:",
            style={"description_width": "initial"},
        )
        self._merge_curve_method_dropdown_selector.observe(
            self._on_merge_curve_method_dropdown_selector_value_changed, names="value"
        )
        self._merge_curve_with_err_bound_label_checkbox = widgets.Checkbox(
            value=True,
            description="Merge with error bound label",
            style={"description_width": "initial"},
        )
        self._merge_curve_with_err_bound_label_checkbox.observe(
            self._on_merge_curve_with_err_bound_label_checkbox_value_changed,
            names="value",
        )
        self._show_error_bounds_checkbox = widgets.Checkbox(
            value=True,
            description="Show error bounds",
            style={"description_width": "initial"},
        )
        self._show_error_bounds_checkbox.observe(self._on_show_error_bounds_checkbox_value_changed, names="value")
        if widgets.__version__ >= "8":
            self._merge_curve_tags_input = widgets.TagsInput(
                value=[],
                allow_duplicates=False,
                placeholder="FedAvg, FedProx:custom-label, etc.",
                # description="Merge tags:",
                # style={"description_width": "initial"},
            )
            self._merge_curve_tags_input.observe(self._on_merge_curve_tags_input_value_changed, names="value")
            merge_curve_tags_box = widgets.VBox(
                [
                    widgets.HBox(
                        [
                            self._merge_curve_method_dropdown_selector,
                            widgets.Label("Merge tags:"),
                            self._merge_curve_tags_input,
                        ]
                    ),
                    widgets.HBox(
                        [
                            self._show_error_bounds_checkbox,
                            self._merge_curve_with_err_bound_label_checkbox,
                        ]
                    ),
                ],
            )
        else:  # TagsInput was added in ipywidgets 8.x
            self._merge_curve_tags_input = None
            merge_curve_tags_box = widgets.HTML(
                "<span style='color:red'>"
                f"Curve merging is not supported for ipywidgets {widgets.__version__}. "
                "Please upgrade to ipywidgets 8.x or above."
                "</span>"
            )

        self._xmin_input = widgets.Text(
            value="",
            # description="X range:",
            # style={"description_width": "80px"},
            layout={"width": "100px"},
        )
        self._xmax_input = widgets.Text(
            value="",
            # description="-",
            # style={"description_width": "80px"},
            layout={"width": "100px"},
        )
        self._ymin_input = widgets.Text(
            value="",
            # description="Y range:",
            # style={"description_width": "80px"},
            layout={"width": "100px"},
        )
        self._ymax_input = widgets.Text(
            value="",
            # description="-",
            # style={"description_width": "80px"},
            layout={"width": "100px"},
        )
        self._xy_range_refresh_button = widgets.Button(
            description="Refresh XY range",
            disabled=False,
            button_style="warning",  # primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Refresh XY range",
            icon="refresh",  # (FontAwesome names without the `fa-` prefix)
            layout={"width": "200px"},
        )
        self._xy_range_refresh_button.on_click(self._on_xy_range_refresh_button_clicked)
        value_range_hbox = widgets.HBox(
            [
                widgets.Label("X range:"),
                self._xmin_input,
                widgets.Label("-"),
                self._xmax_input,
                widgets.Label("Y range:"),
                self._ymin_input,
                widgets.Label("-"),
                self._ymax_input,
                self._xy_range_refresh_button,
            ],
            # layout=widgets.Layout(align_items="flex-start"),
        )

        # canvas for displaying the curves
        self._canvas = widgets.Output(layout={"border": "2px solid black"})

        self._show_button = widgets.Button(
            description="Plot the curves",
            disabled=False,
            button_style="primary",  # 'primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Plot the curves",
            icon="line-chart",  # (FontAwesome names without the `fa-` prefix)
        )
        self._show_fig_flag = False
        self._show_button.on_click(self._on_show_button_clicked)
        self._clear_button = widgets.Button(
            description="Clear",
            disabled=False,
            button_style="danger",  # 'primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Clear",
            icon="eraser",  # (FontAwesome names without the `fa-` prefix)
            # layout={"width": "100px",},
        )
        self._clear_button.on_click(self._on_clear_button_clicked)

        self._style_dropdown_selector = widgets.Dropdown(
            options=["darkgrid", "whitegrid", "dark", "white", "ticks"],
            value="darkgrid",
            description="Style:",
            style={"description_width": "initial"},
            layout={"width": "150px"},
        )
        self._style_dropdown_selector.observe(self._on_style_dropdown_selector_value_changed, names="value")

        self._font_dropdown_selector = widgets.Dropdown(
            options=_fonts_priority,
            value=_fonts_priority[0],
            description="Font family:",
            style={"description_width": "initial"},
            layout={"width": "200px"},
        )
        self._font_dropdown_selector.observe(self._on_font_dropdown_selector_value_changed, names="value")

        self._palette_dropdown_selector = widgets.Dropdown(
            options=_color_palettes,
            value="tab10",
            description="Palette:",
            style={"description_width": "initial"},
            layout={"width": "150px"},
        )
        self._palette_dropdown_selector.observe(self._on_palette_dropdown_selector_value_changed, names="value")

        self._markers_checkbox = widgets.Checkbox(
            value=True,
            description="Show markers",
            style={"description_width": "initial"},
            layout={"width": "150px"},
        )
        self._markers_checkbox.observe(self._on_markers_checkbox_value_changed, names="value")

        self._despine_checkbox = widgets.Checkbox(
            value=False,
            description="Despine",
            style={"description_width": "initial"},
            layout={"width": "100px"},
        )
        self._despine_checkbox.observe(self._on_despine_checkbox_value_changed, names="value")

        self._savefig_dir_input = widgets.Text(
            value="./images",
            description="Save dir:",
            style={"description_width": "initial"},
        )
        self._savefig_filename_input = widgets.Text(
            value="",
            description="Save filename:",
            style={"description_width": "initial"},
            placeholder="only filename, no extension",
            layout={"width": "400px"},
        )
        self._savefig_format_dropdown_selector = widgets.Dropdown(
            value="pdf",
            options=["pdf", "svg", "png", "jpg", "ps"],
            description="Save format:",
            style={"description_width": "initial"},
            layout={"width": "150px"},
        )
        self._savefig_dpi_slider = widgets.IntSlider(
            value=600,
            min=100,
            max=1000,
            step=20,
            description="DPI:",
            orientation="horizontal",
            readout=True,
            readout_format="d",
            style={"description_width": "initial"},
            layout={"width": "300px"},
        )
        self._savefig_button = widgets.Button(
            description="Save",
            disabled=False,
            button_style="success",  # 'primary', 'success', 'info', 'warning', 'danger' or ''
            tooltip="Save",
            icon="save",  # (FontAwesome names without the `fa-` prefix)
        )
        self._savefig_message_area = widgets.Output(layout={"border": "2px solid black"})
        self._savefig_button.on_click(self._on_savefig_button_clicked)

        self._log_file_dropdown_selector = widgets.Dropdown(
            options=list(zip(self.log_files, self._log_files)),
            value=None,
            description="Select log file:",
            disabled=False,
            layout={"width": "500px"},
            style={"description_width": "initial"},
        )
        self._show_config_area = widgets.Output(layout={"border": "2px solid black"})
        self._log_file_dropdown_selector.observe(self._on_log_file_dropdown_selector_change, names="value")

        # layout, from top to bottom
        subdir_selection_hbox = widgets.HBox([self._subdir_dropdown_selector, self._subdir_refresh_button])
        file_filters_hbox = widgets.HBox(
            [
                self._filters_input,
                self._files_refresh_button,
                self._num_log_files_label,
            ]
        )
        # self._log_files_mult_selector
        data_selection_hbox = widgets.HBox(
            [
                self._part_input,
                self._x_metric_input,
                self._y_metric_input,
                self._xlabel_input,
                self._ylabel_input,
                self._refresh_part_metric_button,
            ]
        )
        viz_layout_option_hbox = widgets.HBox(
            [
                self._show_button,
                self._clear_button,
                self._style_dropdown_selector,
                self._palette_dropdown_selector,
                self._font_dropdown_selector,
                self._markers_checkbox,
                self._despine_checkbox,
            ],
            layout=widgets.Layout(align_items="center"),
        )
        # self._canvas
        savefig_box = widgets.VBox(
            [
                widgets.HBox(
                    [
                        self._savefig_dir_input,
                        self._savefig_filename_input,
                        self._savefig_format_dropdown_selector,
                        self._savefig_dpi_slider,
                    ],
                    layout=widgets.Layout(align_items="center"),
                ),
                widgets.HBox(
                    [self._savefig_button, self._savefig_message_area],
                    layout=widgets.Layout(align_items="center"),
                ),
            ],
        )
        # self._log_file_dropdown_selector
        # self._show_config_area

        self._layout = widgets.VBox(
            [
                subdir_selection_hbox,
                file_filters_hbox,
                self._log_files_mult_selector,
                data_selection_hbox,
                slider_box,
                merge_curve_tags_box,
                value_range_hbox,
                viz_layout_option_hbox,
                widgets.Box([self._canvas]),
                savefig_box,
                self._log_file_dropdown_selector,
                self._show_config_area,
            ]
        )

        if self._debug:
            self._debug_message_area = widgets.Output(
                layout={"border": "5px solid red"},
            )
            self._layout.children = self._layout.children + (self._debug_message_area,)

        display(self._layout)

        with self._show_config_area:
            print("Select a log file to show its config.")

    def _on_subdir_dropdown_change(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if change["type"] != "change" or change["name"] != "value":
            return
        self._log_files = find_log_files(
            directory=self._logdir / self._subdir_dropdown_selector.value,
            filters=self._filters_input.value,
        )
        self._log_files_mult_selector.options = list(zip(self.log_files, self._log_files))
        unit = "files" if len(self._log_files) > 1 else "file"
        unit_selected = "files" if len(self._log_files_mult_selector.value) > 1 else "file"
        self._num_log_files_label.value = (
            f"Found {len(self.log_files)} log {unit}. "
            f"Slected {len(self._log_files_mult_selector.value)} log {unit_selected}."
        )
        self._log_file_dropdown_selector.options = list(zip(self.log_files, self._log_files))
        self._log_file_dropdown_selector.value = None
        self._show_config_area.clear_output(wait=False)
        with self._show_config_area:
            print("Select a log file to show its config.")

    def _on_subdir_refresh_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        self._subdir_dropdown_selector.options = ["./"] + [
            d.name for d in self._logdir.iterdir() if d.is_dir() and len(list(d.glob("*.json"))) > 0
        ]

    def _on_files_refresh_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        # update the list of log files
        self._log_files = find_log_files(
            directory=self._logdir / self._subdir_dropdown_selector.value,
            filters=self._filters_input.value,
        )
        # update the options of the selector
        self._log_files_mult_selector.options = list(zip(self.log_files, self._log_files))
        # update the label
        unit = "files" if len(self._log_files) > 1 else "file"
        unit_selected = "files" if len(self._log_files_mult_selector.value) > 1 else "file"
        self._num_log_files_label.value = (
            f"Found {len(self.log_files)} log {unit}. "
            f"Slected {len(self._log_files_mult_selector.value)} log {unit_selected}."
        )
        # update the dropdown selector
        self._log_file_dropdown_selector.options = list(zip(self.log_files, self._log_files))
        # clear loaded curves and stems
        self._fig_curves, self._fig_stems = None, None

    def _log_files_mult_selector_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        # clear self._fig_curves and self._fig_stems
        self._fig_curves, self._fig_stems = None, None
        # update the label
        unit = "files" if len(self._log_files) > 1 else "file"
        unit_selected = "files" if len(self._log_files_mult_selector.value) > 1 else "file"
        self._num_log_files_label.value = (
            f"Found {len(self.log_files)} log {unit}. "
            f"Slected {len(self._log_files_mult_selector.value)} log {unit_selected}."
        )

    def _on_log_file_dropdown_selector_change(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        # clear self._show_config_area
        self._show_config_area.clear_output(wait=True)
        # display the config dict
        if not self._log_file_dropdown_selector.value:
            # empty
            return
        with self._show_config_area:
            config = get_config_from_log(self._log_file_dropdown_selector.value)
            if config:
                # print(json.dumps(config, indent=4))
                print(yaml.dump(config, default_flow_style=False))

    def _on_fig_width_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], (int, float)):
            self._rc_params["figure.figsize"][0] = change["new"]
        if self._show_fig_flag:
            self._show_fig()

    def _on_fig_height_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], (int, float)):
            self._rc_params["figure.figsize"][1] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_x_ticks_font_size_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        self._show_config_area.clear_output(wait=False)
        if isinstance(change["new"], (int, float)):
            self._rc_params["xtick.labelsize"] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_y_ticks_font_size_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], (int, float)):
            self._rc_params["ytick.labelsize"] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_axes_label_font_size_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], (int, float)):
            self._rc_params["axes.labelsize"] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_legend_font_size_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], (int, float)):
            self._rc_params["legend.fontsize"] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_linewidth_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], (int, float)):
            self._rc_params["lines.linewidth"] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_fill_between_alpha_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_moving_average_slider_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_refresh_part_metric_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        # clear the loaded curves and stems
        self._fig_curves, self._fig_stems = None, None
        if self._show_fig_flag:
            self._show_fig()

    def _on_merge_curve_tags_input_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_merge_curve_method_dropdown_selector_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_merge_curve_with_err_bound_label_checkbox_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_show_error_bounds_checkbox_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_xy_range_refresh_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_style_dropdown_selector_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], str) and change["new"] in self._style_dropdown_selector.options:
            sns.set_style(change["new"])
        if self._show_fig_flag:
            self._show_fig()

    def _on_font_dropdown_selector_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], str):
            self._rc_params["font.family"] = change["new"]
            self.reset_matplotlib(rc_params=self._rc_params)
        if self._show_fig_flag:
            self._show_fig()

    def _on_palette_dropdown_selector_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if isinstance(change["new"], str):
            sns.set_palette(change["new"])
        if self._show_fig_flag:
            self._show_fig()

    def _on_markers_checkbox_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_despine_checkbox_value_changed(self, change: dict) -> None:
        if widgets is None or not self._is_notebook:
            return
        if self._show_fig_flag:
            self._show_fig()

    def _on_show_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        self._show_fig()

    def _show_fig(self) -> None:
        # clear the canvas
        self._canvas.clear_output(wait=True)
        self._show_fig_flag = False
        # ensure that log files are selected
        if not self._log_files_mult_selector.value:
            with self._canvas:
                print(colored("No log files selected.", "red"))
            return
        # ensure that part and metrics are specified
        if not self._part_input.value or not self._x_metric_input.value or not self._y_metric_input.value:
            with self._canvas:
                print(colored("Please specify part, x-axis metric, and y-axis metric.", "red"))
            return
        # plot the curves
        with self._canvas:
            try:
                self._show_fig_flag = True
                if self._fig_curves is None or self._fig_stems is None:
                    # first, fetch from self._curve_cache
                    indices = []
                    self._fig_curves, self._fig_stems = [], []
                    for idx, item in enumerate(self._log_files_mult_selector.value):
                        key = self.cache_key(
                            self._part_input.value,
                            self._x_metric_input.value,
                            self._y_metric_input.value,
                            item
                        )
                        if key in self._curve_cache:
                            self._fig_curves.append(self._curve_cache[key])
                            self._fig_stems.append(Path(item).stem)
                            indices.append(idx)
                    if len(indices) < len(self._log_files_mult_selector.value):
                        # second, fetch from the log files
                        for idx, item in enumerate(self._log_files_mult_selector.value):
                            if idx in indices:
                                # skip if already loaded
                                continue
                            (
                                new_fig_curves,
                                new_fig_stems,
                            ) = get_curves_and_labels_from_log(
                                [item for idx, item in enumerate(self._log_files_mult_selector.value) if idx not in indices],
                                part=self._part_input.value,
                                x_metric=self._x_metric_input.value,
                                y_metric=self._y_metric_input.value,
                            )
                        # put the new curves and stems into self._curve_cache
                        for curve, stem in zip(new_fig_curves, new_fig_stems):
                            key = self.cache_key(
                                self._part_input.value,
                                self._x_metric_input.value,
                                self._y_metric_input.value,
                                stem
                            )
                            self._curve_cache[key] = curve
                        # update self._fig_curves and self._fig_stems
                        self._fig_curves.extend(new_fig_curves)
                        self._fig_stems.extend(new_fig_stems)
                    # self._fig_curves, self._fig_stems = get_curves_and_labels_from_log(
                    #     self._log_files_mult_selector.value,
                    #     part=self._part_input.value,
                    #     metric=self._metric_input.value,
                    # )
                xy_range = {}
                for key in ["xmin", "xmax"]:
                    value = getattr(self, f"_{key}_input").value
                    try:
                        xy_range[key] = int(value) if value else None
                    except ValueError:
                        xy_range[key] = None
                for key in ["ymin", "ymax"]:
                    value = getattr(self, f"_{key}_input").value
                    try:
                        xy_range[key] = float(value) if value else None
                    except ValueError:
                        xy_range[key] = None
                if self._debug:
                    with self._debug_message_area:
                        print(f"self._fig_stems: {self._fig_stems}")
                        print(self._debug_log_sep)
                        print(f"xy_range: {xy_range}")
                self.fig, self.ax = plt.subplots(figsize=self._rc_params["figure.figsize"])
                raw_indices = set(range(len(self._fig_curves)))
                linestyle_cycle = itertools.cycle([ls for _, ls in _linestyle_tuple])
                if self._merge_curve_tags_input is not None:
                    # self._merge_curve_tags_input is a list of tags of the form `tag` or `tag:legend_name`
                    _merge_curve_tags_input, _merge_curve_tags_legend = [], []
                    for tag in self._merge_curve_tags_input.value:
                        if ":" in tag:
                            tag, legend = tag.split(":")
                            _merge_curve_tags_input.append(tag)
                            _merge_curve_tags_legend.append(legend)
                        else:
                            _merge_curve_tags_input.append(tag)
                            _merge_curve_tags_legend.append(tag)
                    common_substring = find_longest_common_substring(
                        self._fig_stems,
                        min_len=5,
                        ignore=_merge_curve_tags_input[0] if len(_merge_curve_tags_input) == 1 else None,
                    )
                    if self._debug:
                        with self._debug_message_area:
                            print(f"common_substring: {common_substring}")
                            print(self._debug_log_sep)
                    _fig_stems = [item.replace(common_substring, "-") for item in self._fig_stems]
                    for idx, tag in enumerate(_merge_curve_tags_input):
                        legend = _merge_curve_tags_legend[idx]
                        indices = [
                            idx
                            for idx, stem in enumerate(_fig_stems)
                            if re.search(tag + "\\-", stem) or re.search("\\-" + tag, stem)
                        ]
                        if len(indices) == 0:
                            continue
                        self.fig, self.ax = plot_mean_curve_with_error_bounds(
                            curves=[
                                self._moving_averager(
                                    self._fig_curves[idx]["y"] if isinstance(self._fig_curves[idx], dict) else self._fig_curves[idx],
                                    weight=self._moving_average_slider.value,
                                )
                                for idx in indices
                            ],
                            error_type=self._merge_curve_method_dropdown_selector.value,
                            fig_ax=(self.fig, self.ax),
                            # label=tag,
                            label=legend,
                            show_error_bounds=self._show_error_bounds_checkbox.value,
                            error_bound_label=self._merge_curve_with_err_bound_label_checkbox.value,
                            plot_config={"linestyle": next(linestyle_cycle)},
                            fill_between_config={"alpha": self._fill_between_alpha_slider.value},
                            x_range=(xy_range.get("xmin", None), xy_range.get("xmax", None)),
                            y_range=(xy_range.get("ymin", None), xy_range.get("ymax", None)),
                            x_label=self._x_metric_input.value,
                        )
                        self.ax.get_legend().remove()
                        raw_indices = raw_indices - set(indices)
                raw_indices = sorted(raw_indices)
                if len(raw_indices) > 0:
                    # Apply moving average to curves, handling both dict and array formats
                    smoothed_curves = []
                    for idx in raw_indices:
                        curve = self._fig_curves[idx]
                        if isinstance(curve, dict):
                            # For dict format, apply smoothing to y values and keep x values
                            smoothed_y = self._moving_averager(
                                curve["y"],
                                weight=self._moving_average_slider.value,
                            )
                            smoothed_curves.append({"x": curve["x"], "y": smoothed_y})
                        else:
                            # For legacy array format
                            smoothed_curves.append(
                                self._moving_averager(
                                    curve,
                                    weight=self._moving_average_slider.value,
                                )
                            )

                    self.fig, self.ax = _plot_curves(
                        smoothed_curves,
                        [self._fig_stems[idx] for idx in raw_indices],
                        fig_ax=(self.fig, self.ax),
                        markers=self._markers_checkbox.value,
                        x_range=(xy_range.get("xmin", None), xy_range.get("xmax", None)),
                        y_range=(xy_range.get("ymin", None), xy_range.get("ymax", None)),
                        x_label=self._x_metric_input.value,
                    )
                else:
                    self.ax.legend(loc="best")
                if self._xlabel_input.value:  # not None or empty string
                    self.ax.set_xlabel(self._xlabel_input.value)
                else:
                    self.ax.set_xlabel(self._x_metric_input.value)
                if self._ylabel_input.value:  # not None or empty string
                    self.ax.set_ylabel(self._ylabel_input.value)
                else:
                    self.ax.set_ylabel(f"{self._part_input.value} {self._y_metric_input.value}")
                if self._despine_checkbox.value:
                    if self._style_dropdown_selector.value in ["white", "ticks"]:
                        self.ax.spines.right.set_visible(False)
                        self.ax.spines.top.set_visible(False)
                    if self._style_dropdown_selector.value in ["ticks"]:
                        self.ax.yaxis.set_ticks_position("left")
                        self.ax.xaxis.set_ticks_position("bottom")

                # widgets.widgets.interaction.show_inline_matplotlib_plots()
                # show_inline_matplotlib_plots might not work well for older versions of
                # related packages or systems
                plt.show(self.fig)
            except KeyError:
                print(colored("Invalid part or metric.", "red"))

    def _on_clear_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        self._fig_curves, self._fig_stems = None, None
        self._canvas.clear_output(wait=False)
        self._show_fig_flag = False

    def _on_savefig_button_clicked(self, button: widgets.Button) -> None:
        if widgets is None or not self._is_notebook:
            return
        self._savefig_message_area.clear_output(wait=False)
        with self._savefig_message_area:
            if self._fig_curves is None or self._fig_stems is None:
                print(colored("No figure to save.", "red"))
                return
            if not self._savefig_filename_input.value:
                print(colored("Please specify a filename.", "red"))
                return
            save_fig_dir = Path(self._savefig_dir_input.value).expanduser().resolve()
            save_fig_dir.mkdir(parents=True, exist_ok=True)
            save_fig_filename = save_fig_dir / self._savefig_filename_input.value
            save_fig_filename = save_fig_filename.with_suffix(f".{self._savefig_format_dropdown_selector.value}")
            if save_fig_filename.exists():
                print(colored(f"File {save_fig_filename} already exists.", "red"))
                return
            self.fig.savefig(
                save_fig_filename,
                dpi=self._savefig_dpi_slider.value,
                bbox_inches="tight",
            )
            print(f"Figure saved to {save_fig_filename}")

    def cache_key(self, part: str, x_metric: str, y_metric: str, filename: Union[str, Path]) -> str:
        """Get the cache key for a curve with x and y metrics."""
        return f"{part}-{x_metric}-{y_metric}-{Path(filename).stem}"

    @property
    def log_files(self) -> List[str]:
        return [item.stem for item in self._log_files]

    @property
    def log_files_with_common_substring_removed(self) -> List[str]:
        common_substring = find_longest_common_substring(self.log_files, min_len=5)
        return [item.replace(common_substring, "-") for item in self.log_files]

    @staticmethod
    def reset_matplotlib(rc_params: Optional[Dict[str, Any]] = None) -> None:
        """Reset matplotlib to default settings."""
        if rc_params is None:
            rc_params = mpl.rcParamsDefault
        mpl.rcParams.update(rc_params)
