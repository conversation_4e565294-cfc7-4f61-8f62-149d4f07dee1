\begin{algorithm}[H]
% \SetAlgoNoLine
% \DontPrintSemicolon
\SetKwInOut{Input}{Input}
\Input{step size $s = \frac{1}{\mu} > 0$, $\alpha \in (0, 2)$, error bounds $\varepsilon_{k,0} \geqslant 0$}
{\bfseries Initiation:}\;
\Indp
    {\bfseries Init server:} global model parameters $\theta^{(0)} \in \operatorname{dom}(f)$, $\overline{\theta}^{(0)} = \widetilde{\theta}^{(0)} = \omega^{(0)} = \theta^{(0)} \in \R^d$\;
    {\bfseries Init clients:} $\omega_k^{(0)} = \theta^{(0)}$, $\theta_k^{(0)} \approx \prox_{f_k, \mu}(\omega_k^{(0)})$, $\widehat{\theta}_k^{(0)} = 2\theta_k^{(0)} - \omega_k^{(0)}, ~ \forall k \in [K]$\;
\Indm
\For{$t = 0, 1, \cdots, T-1$}{
    $\mathcal{S}^{(t)} \gets$ (random set of clients) $\subseteq [K]$\;
    each client $k \in \mathcal{S}^{(t)}$ receives $\overline{\theta}^{(t)}$ from server \tcc*[h]{communication}\;
    \For{each client $k \in \mathcal{S}^{(t)}$ {\bfseries in parallel}}{
        choose $\varepsilon_{k,t+1} \geqslant 0$\;
        update $\omega_k^{(t+1)} \gets \omega_k^{(t)} + \alpha(\overline{\theta}^{(t)} - \theta_k^{(t)}),$ \;
        $\theta_k^{(t+1)} \approx \prox_{f_k, \mu}(y_k^{(t+1)})$  \tcc*[h]{inexact local prox step with error bound $\varepsilon_{k,0}$}\;
        $\widehat{\theta}_k^{(t+1)} \gets 2\theta_k^{(t+1)} - \omega_k^{(t+1)}$ \;
        send $\Delta \widehat{\theta}_k^{(t)} = \widehat{\theta}_k^{(t+1)} - \widehat{\theta}_k^{(t)}$ to server\;
        }
    {\bfseries Server Update:}\;
    \Indp
        $\omega^{(t+1)} \gets \omega^{(t)} + \alpha (\overline{\theta}^{(t)} - \omega^{(t)})$\;
        $\widetilde{\theta}^{(t+1)} \gets \widetilde{\theta}^{(t)} + \frac{1}{K}\sum_{k \in \mathcal{S}^{(t)}} \Delta \widehat{\theta}_k^{(t)}$\;
        $\overline{\theta}^{(t+1)} \gets \prox_{g, \frac{K+1}{Ks}} \left( \frac{K}{K+1} \widetilde{\theta}^{(t+1)} + \frac{1}{K+1} \omega^{(t+1)} \right)$ \;
    \Indm
}
\caption{Pseudo-code for \texttt{FedDR}}
\label{algo:feddr}
\end{algorithm}
