\begin{algorithm}[H]
% \SetAlgoNoLine
% \DontPrintSemicolon
\SetKwInOut{Input}{Input}
\Input{server learning rate $\eta_g,$ client learning rates $\eta_k,$ $\forall k \in [K]$}
{\bfseries Initiation:}\;
\Indp
    {\bfseries Server init:} global model parameters $\theta^{(0)} \in \R^d,$ control variates $c^{(0)} \in \R^d$\;
    {\bfseries Clients init:} control variates $c_k^{(0)} \in \R^d, ~ \forall k \in [K]$\;
\Indm
\For{each round $t = 0, 1, \cdots, T-1$}{
    $\mathcal{S}^{(t)} \gets$ (random set of clients) $\subseteq [K]$\;
    broadcast $(\theta^{(t)}, c^{(t)})$ to clients $k \in \mathcal{S}^{(t)}$\;
    \For{each client $k \in \mathcal{S}^{(t)}$ {\bfseries in parallel}}{
        $\theta_k^{(t, 0)} \gets \theta^{(t)}$\;
        \For(\tcc*[h]{$R$ iterates of SGD with variance reduction}){local step $r = 0, 1, \cdots, R-1$}{
            compute mini-batch gradient $g_k^{(t, r)}$ of $\nabla f_k(\theta_k^{(t, r)})$\;
            $\theta_k^{(t, r+1)} \gets \theta_k^{(t, r)} - \eta_k \left( g_k^{(t, r)} - c_k^{(t)} + c^{(t)} \right)$\;
        }
        $c_k^{(t+\frac{1}{2})} \gets \begin{cases} \text{Option 1} & g_k^{(t, 0)} \\ \text{Option 2} & c_k^{(t)} - c^{(t)} + \frac{1}{R\eta_k} \left( \theta^{(t)} - \theta_k^{(t, R)} \right) \end{cases}$\;
        $( \Delta \theta_k^{(t)}, \Delta c_k^{(t)} ) \gets ( \theta_k^{(t, R)} - \theta^{(t)}, c_k^{(t+\frac{1}{2})} - c_k^{(t)} )$\;
        send $( \Delta \theta_k^{(t)}, \Delta c_k^{(t)} )$ to server\;
        $c_k^{(t+1)} \gets c_k^{(t+\frac{1}{2})}$\;
    }
    {\bfseries Server Update:}\;
    \Indp
    $( \Delta \theta^{(t)}, \Delta c^{(t)} ) \gets \frac{1}{\lvert \mathcal{S}^{(t)} \rvert} \sum\limits_{k \in \mathcal{S}^{(t)}} ( \Delta \theta_k^{(t)}, \Delta c_k^{(t)} )$\;
    $\theta^{(t+1)} \gets \theta^{(t)} + \eta_g \Delta \theta^{(t)}$\;
    $c^{(t+1)} \gets c^{(t)} + \frac{\lvert \mathcal{S}^{(t)} \rvert}{K} \Delta c^{(t)}$\;
}
\caption{Pseudo-code for \texttt{SCAFFOLD} (Stochastic Controlled Averaging)}
\label{algo:scaffold}
\end{algorithm}
