\begin{algorithm}[H]
% \SetAlgoNoLine
% \DontPrintSemicolon
\SetKwInOut{Input}{Input}
\Input{learning rate $\eta$, penalty coefficient $\lambda$, $\beta$}
% {\bfseries Server executes:}\;
% \Indp
{\bfseries Initiation:} global (server) model parameters $\theta^{(0)} \in \R^d$\;
\For{each round $t = 0, 1, \cdots, T-1$}{
    $\mathcal{S}^{(t)} \gets$ (random set of clients) $\subseteq [K]$\;
    broadcast $\theta^{(t)}$ to clients $k \in \mathcal{S}^{(t)}$\;
    \For{each client $k \in \mathcal{S}^{(t)}$ {\bfseries in parallel}}{
        $\theta_k^{(t)} \gets$ {\bfseries ClientUpdate}$(k, \theta^{(t)})$\;
        send $\theta_k^{(t)}$ to server\;
    }
    {\bfseries Server Update:}\;
    \Indp
    $\theta^{(t+1)} \gets (1 - \beta) \theta^{(t)} + \frac{\beta}{\lvert \mathcal{S}^{(t)} \rvert} \sum\limits_{k\in \mathcal{S}^{(t)}} \theta_k^{(t)}$\;
    \Indm
}
% \Indm
\vspace{0.2em}
{\bfseries ClientUpdate}$(k, \theta)$: \tcc*[h]{on client $k$}\;
\Indp
$\omega_k^{(t,0)} = \theta_k^{(t,0)} = \theta^{(t)}$\;
\For{local step $r = 0, 1, \cdots, R-1$}{
    $\mathcal{D}_{k, r} \gets$ (sample a mini-batch data)\;
    $\omega_k^{(t,r)} \gets \argmin_{\omega_k} \left\{ \ell_k(\omega_k; \mathcal{D}_{k, r}) - \lambda \langle \omega_k, \theta_k^{(t,r)} \rangle \right\}$\;
    $\theta_k^{(t,r+1)} \gets \theta_k^{(t,r)} - \eta\lambda \left( \theta_k^{(t,r)} - \omega_k^{(t,r)} \right)$\;
}
\Return{$\theta_k^{(t,R)}$}
\caption{Pseudo-code for \texttt{pFedMac}}
\label{algo:pfedmac}
\end{algorithm}
