# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Test Algorithm FedSplit with PyTest

on:
  push:
    branches: [ test-alg ]
  pull_request:
    branches: [ master ]

env:
  TEST_FILENAME: test_fedsplit.py

jobs:
  build:
    # Don't run on forked repos.
    if: contains(from<PERSON><PERSON>('["wenh06"]'), github.repository_owner)

    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: ["3.10"]

    steps:
    - uses: actions/checkout@v4
    - name: Clear unnecessary system components
      run: |
        echo "Free space before cleanup:"
        df -h
        sudo rm -rf /usr/local/lib/android # will release about 10 GB if you don't need Android
        sudo rm -rf /usr/share/dotnet # will release about 20GB if you don't need .NET
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        echo "Free space after cleanup:"
        df -h
    - name: Install system libraries
      run: |
        sudo apt update
        sudo apt install build-essential ffmpeg libsm6 libxext6 libsndfile1 -y
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: List existing Python packages
      run: |
        python -m pip list
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        python -m pip install -r requirements.txt
        python -m pip install pytest pytest-cov # Testing packages
    - name: List installed Python packages
      run: |
        python -m pip list
    - name: Run test with pytest
      run: |
        pytest -vv -s test/test-algorithms/$TEST_FILENAME
