name: Docker Image CI and Test

on:
  push:
    branches: [ docker-ci ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  DOCKER_HUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
  DOCKER_HUB_REPOSITORY: fl-sim

jobs:

  build:
    # Don't run on forked repos.
    if: contains(from<PERSON><PERSON>('["wenh06"]'), github.repository_owner)

    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
    - name: Clear unnecessary system components
      run: |
        echo "Free space before cleanup:"
        df -h
        sudo rm -rf /usr/local/lib/android # will release about 10 GB if you don't need Android
        sudo rm -rf /usr/share/dotnet # will release about 20GB if you don't need .NET
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        echo "Free space after cleanup:"
        df -h
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Login to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    - name: Build and push python3.8-slim version
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./python3.8-slim.Dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:python3.8-slim
    - name: Build and push pytorch1.13.1-cu116 version
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./pytorch1.13.1-cu116.Dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:pytorch1.13.1-cu116
    - name: Build and push pytorch2.0.1-cu117 version
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./pytorch2.0.1-cu117.Dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:pytorch2.0.1-cu117
          ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:latest
    - name: Test the Docker image
      run: |
        docker images  # list local images
        docker run \
          -e PYTHONUNBUFFERED=1 \
          --rm ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:python3.8-slim \
          bash -c "fl-sim example-configs/action-test.yml"
        docker images  # list local images
        docker rmi ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:python3.8-slim  # remove tested image to save space
        docker images  # list local images
        docker run \
          -e PYTHONUNBUFFERED=1 \
          --rm ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:pytorch1.13.1-cu116 \
          bash -c "fl-sim example-configs/action-test.yml"
        docker images  # list local images
        docker rmi ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:pytorch1.13.1-cu116  # remove tested image to save space
        docker images  # list local images
        docker run \
          -e PYTHONUNBUFFERED=1 \
          --rm ${{ env.DOCKER_HUB_USERNAME }}/${{ env.DOCKER_HUB_REPOSITORY }}:pytorch2.0.1-cu117 \
          bash -c "fl-sim example-configs/action-test.yml"
        docker images  # list local images
