# This workflow will build the docs using <PERSON><PERSON>nx and check for errors.

name: Test Docs with <PERSON><PERSON><PERSON> and Publish

on:
  push:
    branches: [ doc, master ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  # Enable bib-lookup
  DB_BIB_LOOKUP: true
  PANDOC_RELEASE: 3.1.7

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: write
  pages: write
  id-token: write

# Allow one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: true

jobs:
  build:
    # Don't run on forked repos.
    if: contains(from<PERSON>son('["wenh06"]'), github.repository_owner)

    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: ["3.9"]

    steps:
    - uses: actions/checkout@v4
    - name: Clear unnecessary system components
      run: |
        echo "Free space before cleanup:"
        df -h
        sudo rm -rf /usr/local/lib/android # will release about 10 GB if you don't need Android
        sudo rm -rf /usr/share/dotnet # will release about 20GB if you don't need .NET
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        echo "Free space after cleanup:"
        df -h
    - name: Install system libraries
      run: |
        sudo apt update
        sudo apt install build-essential ffmpeg libsm6 libxext6 libsndfile1 wget -y
    # - name: Set up Node.js 18.x
    #   uses: actions/setup-node@v3
    #   with:
    #     node-version: 18
    #     cache: 'npm'
    # - name: Install node dependencies
    #   run: npm i pseudocode
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip' # caching pip dependencies
    - name: List existing Python packages
      run: |
        python -m pip list
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel ipython
        python -m pip install -r requirements.txt
        python -m pip install -r docs/requirements.txt
        python -m pip uninstall fl-sim --yes # Remove if already installed
        python setup.py install_egg_info
        pip install -e .[dev]
        pip install jupyter 'ipykernel<5.0.0' 'ipython<7.0.0'
    - name: List installed Python packages
      run: |
        python -m pip list
    - name: Setup TeX Live
      # This is used to render the tikz plots, algorithm2e, etc.
      uses: teatimeguest/setup-texlive-action@v3
      with:
        # List of TeXLive-packages contained in the scheme-basic
        # https://trac.macports.org/wiki/TeXLivePackages
        # tlmgr info --list scheme-basic
        # tlmgr info --list collection-latex
        # tlmgr info --list collection-basic
        packages: >-
          scheme-basic
          algorithm2e
          biber
          biblatex
          pgf
          pgfplots
          relsize
          standalone
          xetex
          xecjk
          ifoddpage
    - name: Install latexmk and pandoc and extra software
      run: |
        sudo apt install latexmk -y
        wget https://github.com/jgm/pandoc/releases/download/$PANDOC_RELEASE/pandoc-$PANDOC_RELEASE-1-amd64.deb
        sudo dpkg -i pandoc-$PANDOC_RELEASE-1-amd64.deb
        sudo rm pandoc-$PANDOC_RELEASE-1-amd64.deb
        sudo apt install texlive-pictures texlive-latex-extra pdf2svg -y
    - name: Build docs with Sphinx and check for errors
      run: |
        cd docs
        python pre_build.py
        make html
        make linkcheck
    - name: Publish to GitHub Pages
      # publish only when pushed to 'doc' and 'master' branches or manually triggered
      if: (github.event_name == 'push' && (contains(fromJson('["refs/heads/doc", "refs/heads/master"]'), github.ref))) || (github.event_name == 'workflow_dispatch')
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/build/html
        publish_branch: gh-pages
        user_name: 'WEN Hao'
        user_email: '<EMAIL>'
