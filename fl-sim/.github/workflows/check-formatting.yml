# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Formatting with pre-commit

on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    # Don't run on forked repos.
    if: contains(from<PERSON><PERSON>('["wenh06"]'), github.repository_owner)

    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: List existing Python packages
      run: |
        python -m pip list
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install --upgrade wheel
        python -m pip install importlib-metadata==4.8.3 # Testing packages
        python -m pip install -r requirements.txt
    - name: List installed Python packages
      run: |
        python -m pip list
    - name: Check code format with pre-commit
      uses: pre-commit/action@v3.0.1
