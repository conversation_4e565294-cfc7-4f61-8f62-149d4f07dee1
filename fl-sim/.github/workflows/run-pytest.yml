# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Run Test with PyTest

on:
  push:
    branches: [ test, dev ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    # Don't run on forked repos.
    if: contains(from<PERSON><PERSON>('["wenh06"]'), github.repository_owner)

    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v4
    - name: Clear unnecessary system components
      run: |
        echo "Free space before cleanup:"
        df -h
        sudo rm -rf /usr/local/lib/android # will release about 10 GB if you don't need Android
        sudo rm -rf /usr/share/dotnet # will release about 20GB if you don't need .NET
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        echo "Free space after cleanup:"
        df -h
    - name: Install system libraries
      run: |
        sudo apt update
        sudo apt install build-essential ffmpeg libsm6 libxext6 libsndfile1 -y
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: List existing Python packages
      run: |
        python -m pip list
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        python -m pip install -r requirements.txt
        python -m pip install pytest pytest-xdist pytest-cov # Testing packages
        python -m pip uninstall fl-sim --yes # Remove if already installed
        python setup.py install_egg_info # Workaround https://github.com/pypa/pip/issues/4537
        python -m pip install -e .[dev] # Install fl-sim
        python -m pip freeze
    - name: List installed Python packages
      run: |
        python -m pip list
    - name: Initialize NLTK
      run: |
        python -c "from fl_sim.models.tokenizers import init_nltk; init_nltk(quiet=False)"
    - name: Run test with pytest and collect coverage
      run: |
        pytest -vv -s \
        --cov-config=.coveragerc \
        --cov-report term-missing \
        --cov=fl_sim \
        --ignore=test/test-algorithms \
        test
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v4
      # only upload coverage reports when python version is 3.10
      if: matrix.python-version == '3.10'
      with:
        fail_ci_if_error: true # optional (default = false)
        verbose: true # optional (default = false)
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
