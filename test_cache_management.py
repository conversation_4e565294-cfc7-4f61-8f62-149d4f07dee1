#!/usr/bin/env python3
"""
Test script for FedCIFAR100_LDA cache management functionality.
Tests the new public interfaces for cache management.
"""

import sys
import warnings
sys.path.append('/home/<USER>/federated-learning/fl-sim')

from fl_sim.data_processing.fed_cifar import FedCIFAR100_LDA

def test_cache_management():
    """Test cache management functionality."""
    
    print("=== FedCIFAR100_LDA Cache Management Test ===\n")
    
    # Initialize dataset
    print("1. Initializing FedCIFAR100_LDA...")
    dataset = FedCIFAR100_LDA(
        lda_alpha=0.1,
        num_clients=10,
        transform="none",
        seed=42
    )
    print("   ✓ Dataset initialized with automatic cache creation")
    
    # Check initial cache status
    print("\n2. Checking initial cache status...")
    cache_info = dataset.get_cache_info()
    print(f"   Cache available: {cache_info['cache_available']}")
    print(f"   Clients cached: {cache_info['num_clients_cached']}")
    print(f"   Memory usage: {cache_info['memory_usage_mb']:.1f} MB")
    
    # Test getting dataloader with cache
    print("\n3. Testing dataloader access with cache...")
    train_dl, test_dl = dataset.get_dataloader(train_bs=32, test_bs=32, client_idx=0)
    print(f"   ✓ Client 0 dataloader created successfully")
    print(f"   Train samples: {len(train_dl.dataset)}")
    print(f"   Test samples: {len(test_dl.dataset)}")
    
    # Check cache status after access
    cache_info = dataset.get_cache_info()
    print(f"   Clients accessed: {cache_info['clients_accessed']}")
    
    # Test clearing cache
    print("\n4. Testing cache clearing...")
    dataset.clear_data_cache()
    
    # Check cache status after clearing
    cache_info = dataset.get_cache_info()
    print(f"   Cache available after clearing: {cache_info['cache_available']}")
    
    # Test getting dataloader without cache (should show warning)
    print("\n5. Testing dataloader access without cache (expect warning)...")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        train_dl, test_dl = dataset.get_dataloader(train_bs=32, test_bs=32, client_idx=1)
        
        if w:
            print(f"   ✓ Warning captured: {w[0].message}")
        else:
            print("   ⚠ No warning was issued")
    
    print(f"   ✓ Client 1 dataloader created (slow method)")
    print(f"   Train samples: {len(train_dl.dataset)}")
    
    # Test recreating cache
    print("\n6. Testing cache recreation...")
    dataset.create_data_cache()
    
    # Check cache status after recreation
    cache_info = dataset.get_cache_info()
    print(f"   Cache available after recreation: {cache_info['cache_available']}")
    print(f"   Clients cached: {cache_info['num_clients_cached']}")
    
    # Test getting dataloader with recreated cache
    print("\n7. Testing dataloader access with recreated cache...")
    train_dl, test_dl = dataset.get_dataloader(train_bs=32, test_bs=32, client_idx=2)
    print(f"   ✓ Client 2 dataloader created successfully (fast method)")
    print(f"   Train samples: {len(train_dl.dataset)}")
    
    # Test cache info utility
    print("\n8. Final cache information...")
    cache_info = dataset.get_cache_info()
    for key, value in cache_info.items():
        print(f"   {key}: {value}")
    
    return True

def test_multiple_access_pattern():
    """Test the typical usage pattern with multiple client accesses."""
    
    print("\n" + "="*60)
    print("MULTIPLE CLIENT ACCESS PATTERN TEST")
    print("="*60)
    
    # Initialize dataset
    dataset = FedCIFAR100_LDA(
        lda_alpha=0.1,
        num_clients=5,
        transform="none",
        seed=42
    )
    
    print(f"\n1. Getting dataloaders for all {dataset.num_clients} clients...")
    
    # Access all clients (should trigger automatic cache cleanup)
    for client_idx in range(dataset.num_clients):
        train_dl, test_dl = dataset.get_dataloader(
            train_bs=32, 
            test_bs=32, 
            client_idx=client_idx
        )
        print(f"   Client {client_idx}: {len(train_dl.dataset)} train, {len(test_dl.dataset)} test")
        
        # Check if cache was automatically cleared
        if not dataset.is_cache_available():
            print(f"   ✓ Cache automatically cleared after all clients accessed")
            break
    
    # Try to access again (should show warning)
    print(f"\n2. Accessing client 0 again after cache cleanup...")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        train_dl, test_dl = dataset.get_dataloader(train_bs=32, test_bs=32, client_idx=0)
        
        if w:
            print(f"   ✓ Warning issued as expected")
        else:
            print(f"   ⚠ No warning (cache might still be available)")
    
    # Recreate cache for future use
    print(f"\n3. Recreating cache for future use...")
    dataset.create_data_cache()
    print(f"   ✓ Cache recreated and ready for fast access")
    
    return True

if __name__ == "__main__":
    print("Testing FedCIFAR100_LDA cache management functionality...\n")
    
    try:
        success1 = test_cache_management()
        success2 = test_multiple_access_pattern()
        
        if success1 and success2:
            print(f"\n🎉 All cache management tests passed!")
            print(f"\n📋 USAGE SUMMARY:")
            print(f"✅ dataset.create_data_cache() - Create/recreate cache for fast access")
            print(f"✅ dataset.clear_data_cache() - Clear cache to free memory")
            print(f"✅ dataset.is_cache_available() - Check if cache exists")
            print(f"✅ dataset.get_cache_info() - Get detailed cache information")
            print(f"⚠️  Warnings are issued when accessing data without cache")
        else:
            print(f"\n❌ Some tests failed!")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "="*60)
    print(f"CACHE MANAGEMENT TEST COMPLETED")
    print(f"="*60)
