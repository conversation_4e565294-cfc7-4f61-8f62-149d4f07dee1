import numpy as np
import h5py
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

# --- 确保您的包可以被正确导入 ---
# 假设您的包名为 fl_sim，并且已通过 `pip install -e .` 安装
try:
    from fl_sim.data_processing.fed_cifar import FedCIFAR100
    from fl_sim.data_processing._noniid_partition import (
        non_iid_partition_with_dirichlet_distribution,
        record_data_stats,
    )
except ImportError as e:
    print("导入错误: 请确保您的包'fl_sim'已正确安装 (例如，使用 'pip install -e .')")
    print(f"原始错误信息: {e}")
    exit()

# --- 1. 配置参数 ---
# 您可以调整这些值来测试不同的场景
NUM_CLIENTS = 100         # 要划分成的客户端数量
LDA_ALPHA = 0.1           # Dirichlet分布的alpha参数。值越小，数据异构性越强。
SEED = 42                 # 随机种子，确保结果可复现

print("--- LDA分割功能快速测试脚本 ---")

# --- 2. 初始化 FedCIFAR100 数据集对象 (这将触发下载) ---
print(f"\n--> 步骤 2: 初始化 FedCIFAR100 数据集对象 (将下载到默认目录)...")
# 我们实例化这个类主要是为了利用它的数据下载和文件路径管理功能
# transform='none' 表示我们只关心原始数据，不做任何图像变换
fed_cifar100 = FedCIFAR100(datadir=None, transform='none', seed=SEED)
print("数据集对象创建成功，数据已准备就绪。")


# --- 3. 从H5文件中加载所有训练标签 ---
print("\n--> 步骤 3: 从H5文件中加载所有训练标签...")
train_h5_path = fed_cifar100.datadir / fed_cifar100.DEFAULT_TRAIN_FILE
all_train_labels = []

# FedCIFAR100 将数据预先分成了500个训练客户端
# 我们需要将它们合并成一个大的数据集再进行重新划分
with h5py.File(train_h5_path, 'r') as f:
    # 访问内部属性来获取客户端ID列表和数据结构名称
    client_ids = list(f[fed_cifar100._EXAMPLE].keys())
    for cid in client_ids:
        all_train_labels.append(f[fed_cifar100._EXAMPLE][cid][fed_cifar100._LABEL][()])

all_train_labels = np.concatenate(all_train_labels)
TOTAL_SAMPLES = len(all_train_labels)
print(f"总共加载了 {TOTAL_SAMPLES} 个训练标签。")


# --- 4. 使用 alpha 对数据索引进行LDA分割 ---
print(f"\n--> 步骤 4: 使用 alpha={LDA_ALPHA} 对 {NUM_CLIENTS} 个客户端进行LDA分割...")
# 为分割过程本身设置随机种子
np.random.seed(SEED)

partition_map = non_iid_partition_with_dirichlet_distribution(
    label_list=all_train_labels,
    client_num=NUM_CLIENTS,
    classes=fed_cifar100.n_class,
    alpha=LDA_ALPHA,
    task="classification"
)
print("分割完成。")


# --- 5. 验证分割的有效性 ---
print("\n--> 步骤 5: 验证分割的有效性...")
total_partitioned_samples = sum(len(indices) for indices in partition_map.values())
print(f"  [检查1] 原始总样本数: {TOTAL_SAMPLES}")
print(f"  [检查1] 分割后总样本数: {total_partitioned_samples}")
assert total_partitioned_samples == TOTAL_SAMPLES, "错误: 分割后的总样本数与原始不符!"
print("  ✅ 总样本数一致。")

all_indices = np.concatenate(list(partition_map.values()))
unique_indices_count = len(np.unique(all_indices))
print(f"\n  [检查2] 分割后的索引总数: {len(all_indices)}")
print(f"  [检查2] 分割后的唯一索引数: {unique_indices_count}")
assert len(all_indices) == unique_indices_count, "错误: 客户端之间存在重复的样本索引!"
assert unique_indices_count == TOTAL_SAMPLES, "错误: 分割后的唯一索引数与原始总样本数不符!"
print("  ✅ 客户端之间无样本重叠，且所有样本都被分配。")
print("\n🎉 分割有效性检查通过！")


# --- 6. 可视化数据分布 ---
print("\n--> 步骤 6: 可视化数据分布...")
# 使用 record_data_stats 辅助函数获取统计信息
data_stats = record_data_stats(all_train_labels, partition_map)

# 转换为 Pandas DataFrame 以便绘图
stats_df = pd.DataFrame(data_stats).fillna(0).astype(int)

# 绘制堆叠条形图
# 为了图表清晰，我们只展示前20个客户端的分布
CLIENTS_TO_SHOW = min(NUM_CLIENTS, 20)
stats_df.T.iloc[:CLIENTS_TO_SHOW].plot(
    kind="bar",
    stacked=True,
    figsize=(15, 8),
    legend=False # 类别太多(100)，图例会很乱，此处不显示
)

plt.title(f"CIFAR-100 数据分布 (前 {CLIENTS_TO_SHOW} 个客户端, Alpha = {LDA_ALPHA})", fontsize=16)
plt.xlabel("客户端 ID", fontsize=12)
plt.ylabel("样本数量", fontsize=12)
plt.xticks(rotation=0)
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
print("正在显示分布图，关闭图表窗口后程序将退出。")
plt.show()