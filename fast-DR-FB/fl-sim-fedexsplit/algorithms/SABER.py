# saber.py

"""
Implementation of the SABER algorithm using the fl_sim.nodes base classes.

Based on the paper: "SABER: A Stochastic Variance-Reduced Algorithm for Byzantine-Tolerant Federated Learning"
(although this implementation focuses on the core variance reduction, not the Byzantine tolerance part).

The implementation adapts the original SABER algorithm to the typical FL communication
pattern by using a one-round-delayed variance reduction term `v`. At round `k`, clients
use `v_{k-1}` to perform their local updates, and the server computes `v_k` at the end
of the round for use in round `k+1`.
"""

import random
import warnings
import logging
from typing import Any, Dict, List, Optional, Sequence

import torch
from torch import Tensor
from torch.nn.parameter import Parameter
from torch_ecg.utils.misc import add_docstring
from tqdm.auto import tqdm

# Assuming these are the correct import paths for your fl-sim framework
from fl_sim.data_processing.fed_dataset import FedDataset
from fl_sim.nodes import Client, ClientConfig, ClientMessage, Server, ServerConfig
from fl_sim.algorithms._misc import client_config_kw_doc, server_config_kw_doc
from fl_sim.algorithms._register import register_algorithm

__all__ = [
    "SABERServer",
    "SABERClient",
    "SABERServerConfig",
    "SABERClientConfig",
]


@register_algorithm()
@add_docstring(server_config_kw_doc, "append")
class SABERServerConfig(ServerConfig):
    """
    Server configuration for the SABER algorithm.

    Parameters
    ----------
    num_iters : int
        The number of communication rounds (outer iterations).
    num_clients : int
        The total number of clients.
    clients_sample_ratio : float
        The ratio of clients to sample for participation in each round.
    p : float
        The probability of synchronization (using a fresh gradient).
    **kwargs : dict, optional
        Additional keyword arguments.
    """

    __name__ = "SABERServerConfig"

    def __init__(
        self,
        num_iters: int,
        num_clients: int,
        clients_sample_ratio: float,
        p: float,
        clients_sample_for_v_ratio: float,
        **kwargs: Any,
    ) -> None:
        name = self.__name__.replace("ServerConfig", "")
        if kwargs.pop("algorithm", None) is not None:
            warnings.warn(
                f"The `algorithm` argument is fixed to `{name}` and will be ignored.",
                RuntimeWarning,
            )
        super().__init__(
            name,
            num_iters,
            num_clients,
            clients_sample_ratio,
            **kwargs,
        )
        self.p = p
        self.clients_sample_for_v_ratio = clients_sample_for_v_ratio


@register_algorithm()
@add_docstring(client_config_kw_doc, "append")
class SABERClientConfig(ClientConfig):
    """
    Client configuration for the SABER algorithm.

    Parameters
    ----------
    batch_size : int
        Batch size for local training (SGD for the proximal subproblem).
    num_epochs : int
        Number of local epochs to run SGD for solving the proximal subproblem.
    lr : float, default 1e-2
        Learning rate for the local SGD optimizer.
    eta : float, default 1.0
        The stepsize `η` in the SABER algorithm, which also defines the proximal term as 1/(2*eta).
    **kwargs : dict, optional
        Additional keyword arguments:
        - optimizer: Name of the optimizer for the proximal step (fixed to "ProxSGD").
    """

    __name__ = "SABERClientConfig"

    def __init__(
        self,
        batch_size: int,
        num_epochs: int,
        lr: float = 1e-2,
        eta: float = 0.05,
        beta: float = 0.03,
        **kwargs: Any,
    ) -> None:
        name = self.__name__.replace("ClientConfig", "")
        if kwargs.pop("algorithm", None) is not None:
            warnings.warn(
                f"The `algorithm` argument is fixed to `{name}` and will be ignored.",
                RuntimeWarning,
            )
        # The optimizer for SABER's local problem is ProxSGD
        optimizer = "ProxSGD"
        if kwargs.pop("optimizer", None) is not None:
            warnings.warn(
                "The `optimizer` argument is fixed to `ProxSGD` and will be ignored.",
                RuntimeWarning,
            )
        super().__init__(
            name,
            optimizer,
            batch_size,
            num_epochs,
            lr,
            **kwargs,
        )
        # In SABER, the proximal coefficient is 1/eta
        # self.optimizer.prox is 1/eta in this case.
        self.eta = eta
        self.prox = 1 / eta
        self.beta = beta


@register_algorithm()
@add_docstring(
    Server.__doc__.replace(
        "The class to simulate the server node.",
        "Server node for the SABER algorithm."
    )
    .replace("ServerConfig", "SABERServerConfig")
    .replace("ClientConfig", "SABERClientConfig")
)
@register_algorithm()
class SABERServer(Server):
    """
    Server node for the SABER algorithm, with a correctly re-architected training loop
    that faithfully implements the original pseudocode within a two-stage communication round.
    """

    __name__ = "SABERServer"

    def _post_init(self) -> None:
        """Initializes server state for SABER."""
        super()._post_init()
        # w_{k-1}, starts as w_0
        self._model_prev = self.get_detached_model_parameters()
        # v_k, v_{k-1}, will be initialized to v_0 = ∇f(w_0)
        self._v_prev = [torch.zeros_like(p) for p in self.model.parameters()]

        # Perform a setup round to initialize v_0 = ∇f(w_0)
        self._logger_manager.log_message("Server performing setup round to initialize v_0...")
        if self._clients is None:
            self._setup_clients()
        
        all_clients = self._clients
        messages = []
        for client in tqdm(all_clients, desc="Initializing v_0", mininterval=1.0, disable=self.config.verbose < 1):
            grad = client.compute_gradients(at=self.model.parameters())
            messages.append({"grad": grad, "n_samples": len(client.train_loader.dataset)})

        total_samples = sum(m["n_samples"] for m in messages)
        if total_samples > 0:
            for m in messages:
                weight = m["n_samples"] / total_samples
                for p_v, p_grad in zip(self._v_prev, m["grad"]):
                    p_v.add_(p_grad.to(self.device), alpha=weight)
        
        # At this point, self._v_prev holds ∇f(w_0).
        self._logger_manager.log_message("Initialization of v_0 complete.")

    @property
    def client_cls(self) -> type:
        return SABERClient # Assumes SABERClient is defined elsewhere
    
    # THIS METHOD IS OVERRIDDEN FROM THE BASE SERVER CLASS
    def train(self, mode: str = "federated", extra_configs: Optional[dict] = None) -> None:
        """
        Custom training method for SABER.
        Always use federated training.
        """
        if self._complete_experiment:
            # reset before training if a previous experiment is completed
            self._reset()
        self._complete_experiment = False
        if mode.lower() != "federated":
            self._logger_manager.log_message(f"Training mode {mode} is not supported. Will use federated training.", 
                                             level=logging.INFO)
        self.train_federated(extra_configs)
        self._complete_experiment = True

    # THIS METHOD IS OVERRIDDEN FROM THE BASE SERVER CLASS
    def train_federated(self, extra_configs: Optional[dict] = None) -> None:
        """
        Overridden federated training loop for SABER. Each round consists of two stages:
        1. Compute the variance reduction term `v_k`.
        2. Perform local client updates using `v_k`.
        """
        if self._clients is None:
            self._setup_clients()

        if self._complete_experiment:
            self._reset()

        self._complete_experiment = False
        self._logger_manager.log_message("Starting SABER federated training...")
        self.n_iter = 0

        # Initial report for multiprocessing progress
        if self._subprocess:
            self._report_progress(n_iter=0, num_iters=self.config.num_iters, training_phase="training")

        with tqdm(range(self.config.num_iters), total=self.config.num_iters, desc="SABER Training", unit="round") as pbar:
            for self.n_iter in pbar:
                current_w_k = self.get_detached_model_parameters()

                # --- STAGE 1: COMPUTE v_k ---
                pbar.set_description(f"Round {self.n_iter+1} [Stage 1: Compute v_k]")
                
                # Decide which branch to take for computing v_k
                use_sync_branch = random.uniform(0, 1) < self.config.p

                # Sample clients for the main update in this round
                clients_for_update_ids = self._sample_clients()

                v_k: List[Tensor]
                # for iter 0, skip v_k update
                if self.n_iter == 0:
                    v_k = self._v_prev
                # for iter 1, use sync branch
                elif use_sync_branch:
                    self._logger_manager.log_message(f"Round {self.n_iter+1}: Computing v_k via SYNC.")
                    # Sample a new set of clients for gradient computation
                    clients_for_grad_ids = self._sample_clients(clients_sample_ratio=self.config.clients_sample_for_v_ratio)
                    v_k = self._get_gradients_from_clients(clients_for_grad_ids, at=current_w_k)
                else:
                    self._logger_manager.log_message(f"Round {self.n_iter+1}: Computing v_k via VR.")
                    # Reuse the main update clients to compute gradient differences
                    # This is a practical choice to avoid a third communication.
                    grad_diff = self._get_gradient_diffs_from_clients(clients_for_update_ids, current_w_k, self._model_prev)
                    
                    v_k = [
                        v_prev.to(self.device) + g_diff
                        for v_prev, g_diff in zip(self._v_prev, grad_diff)
                    ]

                # --- STAGE 2: CLIENT LOCAL UPDATES ---
                pbar.set_description(f"Round {self.n_iter+1} [Stage 2: Client Updates]")
                self._received_messages = []
                # Report progress for multiprocessing
                if self._subprocess:
                    client_progress_count = 0
                # NOTE: evaluate centralized model on server val loader
                if self.server_val_loader:
                    # TODO: reimplement evaluate logic
                    metrics = self.evaluate_centralized(self.server_val_loader)
                    self._logger_manager.log_metrics(
                        None,
                        metrics,
                        step=self._num_communications,  # NOTE: use number of communications as step for now
                        epoch=self.n_iter,
                        part="val",
                    )
                for client_id in tqdm(clients_for_update_ids, desc=f"Client Updates", leave=False):
                    client = self._clients[client_id]
                    
                    # Server sends w_k and the just-computed v_k
                    self._communicate(client)
                    client.accept_parameters()
                    # evaluate immediately after broadcasting
                    if self.n_iter > 0 and (self.n_iter + 1) % self.config.eval_every == 0:
                        part = "train" # only evaluate train metrics on clients
                        # NOTE: one should execute `client.evaluate`
                        # before `client._update`,
                        # otherwise the evaluation would be done
                        # on the ``personalized`` (locally fine-tuned) model.
                        metrics = client.evaluate(part)
                        self._logger_manager.log_metrics(
                            client_id,
                            metrics,
                            step=self.n_iter,
                            epoch=self.n_iter,
                            part=part,
                        )
                    # client trains the model
                    # and perhaps updates other local variables
                    client._update()  # Client solves its prox problem
                    # client communicates with server
                    # typically sending the local model
                    # and evaluated local metrics to the server
                    # and perhaps other local variables (e.g. gradients, etc.)
                    client._communicate(self) # Client sends back w_{k+1,m}

                    # Report progress for multiprocessing
                    if self._subprocess:
                        client_progress_count += 1
                        self._report_progress(
                            n_iter=self.n_iter + 1,
                            num_iters=self.config.num_iters,
                            current_client_progress=client_progress_count,
                            selected_clients_count=len(clients_for_update_ids),
                            training_phase="training"
                        )
                
                # Evaluation(aggregate client metrics) before server update
                if self.n_iter > 0 and (self.n_iter + 1) % self.config.eval_every == 0:
                    # server aggregates the metrics from clients
                    self.aggregate_client_metrics()
                
                # --- ROUND END: SERVER AGGREGATION AND STATE UPDATE ---
                # 0. Preparation for server update, this part is typically done in self._update()
                self._logger_manager.log_message("Server update...")
                if len(self._received_messages) == 0:
                    warnings.warn(
                        "No message received from the clients, unable to update server model.",
                        RuntimeWarning,
                    )
                    return
                assert all([isinstance(m, ClientMessage) for m in self._received_messages]), (
                    "received messages must be of type `ClientMessage`, "
                    f"but got {[type(m) for m in self._received_messages if not isinstance(m, ClientMessage)]}"
                )
                # 1. Aggregate client models (w_{k+1,m}) to get w_{k+1}
                # This updates self.model
                if len(self._received_messages) > 0:
                    self.avg_parameters(size_aware=True)
                
                # 2. Update server state for the next round
                self._model_prev = current_w_k  # w_k becomes w_{k-1} for the next round
                self._v_prev = v_k          # v_k is stored for the next VR step
                
                # 3. Clear received messages
                self._received_messages = []
                    
                # no need for self._update() here since all updates are done in train_federated

        self._logger_manager.log_message("SABER federated training finished.")
        self._logger_manager.flush()
        self._complete_experiment = True

    def _get_gradients_from_clients(self, client_ids: List[int], at: List[Tensor]) -> List[Tensor]:
        """Helper to get averaged gradients from a set of clients."""
        messages = []
        for client_id in client_ids:
            client = self._clients[client_id]
            grad = client.compute_gradients(at=at)
            self._num_communications += 2 # 2 messages per client sent for gradient computation
            messages.append({"grad": grad, "n_samples": len(client.train_loader.dataset)})
        
        avg_grad = [torch.zeros_like(p, device=self.device) for p in self.model.parameters()]
        total_samples = sum(m["n_samples"] for m in messages)
        if total_samples > 0:
            for m in messages:
                weight = m["n_samples"] / total_samples
                for p_avg, p_grad in zip(avg_grad, m["grad"]):
                    p_avg.add_(p_grad.to(self.device), alpha=weight)
        return avg_grad

    def _get_gradient_diffs_from_clients(self, client_ids: List[int], w_k: List[Tensor], w_k_prev: List[Tensor]) -> List[Tensor]:
        """Helper to get averaged gradient differences from a set of clients."""
        messages = []
        for client_id in client_ids:
            client = self._clients[client_id]
            grad_k = client.compute_gradients(at=w_k)
            grad_k_prev = client.compute_gradients(at=w_k_prev)
            grad_diff = [g1 - g2 for g1, g2 in zip(grad_k, grad_k_prev)]
            self._num_communications += 2 # 2 messages per client sent for gradient computation
            messages.append({"grad_diff": grad_diff, "n_samples": len(client.train_loader.dataset)})

        avg_grad_diff = [torch.zeros_like(p, device=self.device) for p in self.model.parameters()]
        total_samples = sum(m["n_samples"] for m in messages)
        if total_samples > 0:
            for m in messages:
                weight = m["n_samples"] / total_samples
                for p_avg_diff, p_diff in zip(avg_grad_diff, m["grad_diff"]):
                    p_avg_diff.add_(p_diff.to(self.device), alpha=weight)
        return avg_grad_diff
    
    def communicate(self, target: "Node") -> None:
        target._received_messages = {
                "w_k": self.get_detached_model_parameters(),
                "v_k": self._v_prev,
                "task": "local_update" # Tell client what to do
            }

    # The original update method is now irrelevant.
    def update(self) -> None:
        pass # All logic is in train_federated

    @property
    def config_cls(self) -> Dict[str, type]:
        return {"server": SABERServerConfig, "client": SABERClientConfig}
    
    @property
    def required_config_fields(self) -> List[str]:
        return ["p"]

    @property
    def doi(self) -> List[str]:
        return ["not published yet"]


@register_algorithm()
@add_docstring(
    Client.__doc__.replace(
        "The class to simulate the client node.",
        "Client node for the SABER algorithm."
    ).replace("ClientConfig", "SABERClientConfig")
)
class SABERClient(Client):
    """Client node for the SABER algorithm."""

    __name__ = "SABERClient"

    @property
    def required_config_fields(self) -> List[str]:
        return ["eta"]

    def communicate(self, target: "SABERServer") -> None:
        """Sends the updated local model to the server."""
        # This is only called after a local_update task
        message = ClientMessage(
            client_id=self.client_id,
            parameters=self.get_detached_model_parameters(), # w_{k+1, m}
            train_samples=len(self.train_loader.dataset),
            metrics=self._metrics, # Metrics can be added here if needed
        )
        target._received_messages.append(message)
        
    def accept_parameters(self) -> None:
        """
        Accepts parameters from the server.
        """
        self.set_parameters(self._received_messages["w_k"])

    def update(self) -> None:
        """
        Receives data and a task from the server and executes it.
        This client no longer needs to compute gradients for the server's v_k update,
        as the server will call client.compute_gradients() directly.
        """
        task = self._received_messages.get("task")
        if task == "local_update":
            # Set the model to w_k as the starting point for the prox solve
            w_k = [p.detach().clone().to(self.device) for p in self._received_messages["w_k"]]
            self.set_parameters(w_k)
            self.solve_inner() # Alias for self.train()
        else:
            warnings.warn(f"Client {self.client_id} received unknown task: {task}", RuntimeWarning)

    def train(self) -> None:
        """
        Implements the core SABER local proximal update.
        """
        # Get w_k and v_k from the received message
        w_k_buffer = [p.detach().clone().to(self.device) for p in self._received_messages["w_k"]]
        v_k_buffer = [p.detach().clone().to(self.device) for p in self._received_messages["v_k"]]

        # --- Compute the argument for the proximal operator ---
        # The local objective is: argmin_w f_m(w) + <v_k - ∇f_m(w_k), w - w_k> + (1/2η) ||w - w_k||^2
        # To use ProxSGD, we need to find z such that the objective is equivalent to:
        # f_m(w) + (1/(2η)) ||w - z||^2
        # By completing the square:
        # z = w_k - η * (v_k - ∇f_m(w_k))
        # This requires ∇f_m(w_k), let's compute it.
        grad_at_wk = self.compute_gradients(at=w_k_buffer)

        prox_arg = []
        with torch.no_grad():
            for p_wk, p_vk, p_grad_wk in zip(w_k_buffer, v_k_buffer, grad_at_wk):
                control_variate = p_vk - p_grad_wk
                z_p = p_wk - self.config.beta * control_variate
                prox_arg.append(z_p.detach().clone())

        # --- Solve the proximal subproblem using local SGD ---
        # self.model is already at w_k, a good starting point.
        self.model.train()
        for epoch in range(self.config.num_epochs):
            for X, y in self.train_loader:
                X, y = X.to(self.device), y.to(self.device)
                
                self.optimizer.zero_grad()
                output = self.model(X)
                loss = self.criterion(output, y)
                loss.backward()
                
                self.optimizer.step(local_weights=prox_arg)

        self.lr_scheduler.step()