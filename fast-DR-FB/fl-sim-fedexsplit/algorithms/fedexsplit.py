"""
Implementation of the FedExSplit algorithm using the fl_sim.nodes base classes.

Based on the Easy-DR framework. Requires clients to maintain local state and compute gradients
at the global model state.
"""

import warnings
from typing import Any, Dict, List, Optional, Sequence

import torch
import torch.nn as nn
from torch import Tensor
from torch.nn.parameter import Parameter
from torch_ecg.utils.misc import add_docstring
from tqdm.auto import tqdm

# The following are relative imports for built-in algorithms
# from ...data_processing.fed_dataset import FedDataset
# from ...nodes import Client, ClientConfig, ClientMessage, Server, ServerConfig
# from .._misc import client_config_kw_doc, server_config_kw_doc
# from .._register import register_algorithm

# The following are absolute imports for custom algorithms
from fl_sim.data_processing.fed_dataset import FedDataset
from fl_sim.nodes import Client, ClientConfig, ClientMessage, Server, ServerConfig
from fl_sim.algorithms._misc import client_config_kw_doc, server_config_kw_doc
from fl_sim.algorithms._register import register_algorithm

__all__ = [
    "FedExSplitServer",
    "FedExSplitClient",
    "FedExSplitServerConfig",
    "FedExSplitClientConfig",
]


@register_algorithm()
@add_docstring(server_config_kw_doc, "append")
class FedExSplitServerConfig(ServerConfig):
    """
    Server configuration for the FedExSplit algorithm.

    Parameters
    ----------
    num_iters : int
        The number of communication rounds (outer iterations).
    num_clients : int
        The total number of clients.
    clients_sample_ratio : float
        The ratio of clients to sample for participation in each round.
    alg_type : str, default "G1G2"
        The algorithm type, can be "G1G2" | "G1H1" | "G2H2".
    **kwargs : dict, optional
        Additional keyword arguments.
    """

    __name__ = "FedExSplitServerConfig"

    def __init__(
        self,
        num_iters: int,
        num_clients: int,
        clients_sample_ratio: float,
        alg_type: str = "G1G2",
        **kwargs: Any,
    ) -> None:
        name = self.__name__.replace("ServerConfig", "")
        if kwargs.pop("algorithm", None) is not None:
            warnings.warn(
                f"The `algorithm` argument is fixed to `{name}` and will be ignored.",
                RuntimeWarning,
            )
        super().__init__(
            name,
            num_iters,
            num_clients,
            clients_sample_ratio,
            **kwargs,
        )
        self.alg_type = alg_type


@register_algorithm()
@add_docstring(client_config_kw_doc, "append")
class FedExSplitClientConfig(ClientConfig):
    """
    Client configuration for the FedExSplit algorithm.

    Parameters
    ----------
    batch_size : int
        Batch size for local training (SGD for the proximal subproblem).
    num_epochs : int
        Number of local epochs to run SGD for solving the proximal subproblem.
    lr : float, default 1e-2
        Learning rate for the local SGD optimizer.
    beta : float, default 1.0
        Step size for the gradient term \\(g_i\\).
    gamma : float, default 1.0
        Step size for the error term \\(e_i\\).
    mu : float, default 1.0
        Coefficient for the proximal term \\(\\|x - v_i^k\\|^2\\).
    **kwargs : dict, optional
        Additional keyword arguments:
        - optimizer: Name of the optimizer for the proximal step (default "SGD").
    """

    __name__ = "FedExSplitClientConfig"

    def __init__(
        self,
        batch_size: int,
        num_epochs: int,
        lr: float = 1e-2,
        beta: float = 1e-2,
        gamma: float = 1.0,
        mu: float = 1e-2,
        **kwargs: Any,
    ) -> None:
        # Store FedExSplit specific parameters
        self.alg_type = None # will be set at server side in server.__init__()
        self.beta = beta
        self.gamma = gamma
        self.mu = mu

        name = self.__name__.replace("ClientConfig", "")
        if kwargs.pop("algorithm", None) is not None:
            warnings.warn(
                f"The `algorithm` argument is fixed to `{name}` and will be ignored.",
                RuntimeWarning,
            )
        # Default optimizer for the inner prox solve is SGD
        optimizer = "ProxSGD"
        if kwargs.pop("optimizer", None) is not None:
            warnings.warn(
                "The `optimizer` argument is fixed to `ProxSGD` and will be ignored.",
                RuntimeWarning,
            )

        super().__init__(
            name,
            optimizer,  # Optimizer used to solve the prox subproblem
            batch_size,
            num_epochs,
            lr,
            **kwargs,
        )


@register_algorithm()
@add_docstring(
    Server.__doc__.replace(
        "The class to simulate the server node.",
        "Server node for the FedExSplit algorithm."
    )
    .replace("ServerConfig", "FedExSplitServerConfig")
    .replace("ClientConfig", "FedExSplitClientConfig")
)
class FedExSplitServer(Server):
    """
    Server node for the FedExSplit (Easy-DR based) algorithm.
    Aggregates client models via simple averaging.
    """

    __name__ = "FedExSplitServer"
    
    def __init__(
        self,
        model: nn.Module,
        dataset: FedDataset,
        config: ServerConfig,
        client_config: ClientConfig,
        lazy: bool = False,
        is_subprocess: bool = False,
    ) -> None:
        if client_config.alg_type is None:
            client_config.alg_type = config.alg_type
        elif client_config.alg_type != config.alg_type:
            raise ValueError(f"client_config.alg_type ({client_config.alg_type}) != config.alg_type ({config.alg_type})")
        super().__init__(
            model,
            dataset,
            config,
            client_config,
            lazy,
            is_subprocess,
        )

    @property
    def required_config_fields(self) -> List[str]:
        """No extra server-specific config fields needed for basic FedExSplit."""
        return []

    @property
    def client_cls(self) -> type:
        """The class of the client node."""
        return FedExSplitClient
    
    def pre_iter(self) -> None:
        """
        Instructions for different types
        ================================
        G1G2
        ----
        1. send x_avg^k to client
        2. client calculates local gradient g_i(x_avg^k)
        
        G1H1
        ----
        1. send x_avg^k to client
        2. client calculates local gradient g_i(x_i^k) and send to server
        3. server send aggregated gradient to client
        
        G2H2
        ----
        1. send x_avg^k to client
        2. client calculates local gradient g_i(x_avg^k) and send to server
        3. server send aggregated gradient to client
        """
        self._received_messages_for_gradient = []
        # 1. send x_avg^k to client
        self.broadcast(self.get_detached_model_parameters(), "parameters")
        # 2. differs for alg_type but done in client.pre_iter()
        for client_id in self.selected_clients:
            client = self._clients[client_id]
            client.pre_iter()
            if self.config.alg_type == "G1H1" or self.config.alg_type == "G2H2":
                task_type = "H1" if self.config.alg_type == "G1H1" else "H2"
                client.communicate_for_gradient(self)
        # 3 server aggregates local gradient. Compute H1 or H2.
        if self.config.alg_type == "G1H1" or self.config.alg_type == "G2H2":
            self.broadcast(self.avg_received_gradients(size_aware = True), "gradient")
        
    def broadcast(self, content: Any, label: str) -> None:
        """Broadcast to all selected client with label and content."""
        for client_id in self.selected_clients:
            client = self._clients[client_id]
            content = [p.to(client.device).detach() for p in content]
            client._received_messages[label] = content
            self._num_communications += 1
            
    def avg_received_gradients(self, size_aware: bool = False) -> None:
        """Averaging gradients received from the clients.

        Parameters
        ----------
        size_aware : bool, default False
            Whether to use the size-aware averaging,
            which is the weighted average of the parameters,
            where the weight is the number of training samples.
            From the view of optimization theory,
            this is recommended to be set `False`.

        Returns
        -------
        None

        """
        if len(self._received_messages_for_gradient) == 0:
            raise ValueError("No gradients received from clients.")
        # structure of self._received_messages_for_gradient:
        # [{'client_id': 0, 'train_samples': 40, 'metrics': None, 'gradient': [grad_p1, grad_p2, ...]}, {...}, ...]
        # generate zeros like grad
        grad = [torch.zeros_like(p, device=self.device) for p in self._received_messages_for_gradient[0]["gradient"]]
        total_samples = sum([m["train_samples"] for m in self._received_messages_for_gradient])
        for m in self._received_messages_for_gradient:
            ratio = (m["train_samples"] / total_samples if size_aware else 1 / len(self._received_messages_for_gradient))
            for i, p_mg in enumerate(m["gradient"]):
                # Add weighted gradient to the accumulated gradient
                grad[i] += ratio * p_mg.to(self.device)
        return grad

    def _communicate(self, target: "Client") -> None:
        """Broadcast to target client, and maintain state variables."""
        # Overwrite the _communicate method to handle different types of algorithms
        # Common communication is bypassed
        pass
    
    def communicate(self, target: "FedExSplitClient") -> None:
        """Sends the current global model parameters to the target client."""
        # Overwrite the _communicate method to handle different types of algorithms
        # Common communication is bypassed
        pass

    def update(self) -> None:
        """Aggregates the received client models using simple averaging."""
        # FedExSplit aggregates the updated local models x_i^{k+1}
        self.avg_parameters(size_aware=False) # x_avg^{k+1} = (1/N) * sum(x_i^{k+1})

    @property
    def config_cls(self) -> Dict[str, type]:
        """Returns the configuration classes for server and client."""
        return {
            "server": FedExSplitServerConfig,
            "client": FedExSplitClientConfig,
        }

    @property
    def doi(self) -> List[str]:
        """DOI(s) related to the Easy-DR or similar methods if available."""
        # Placeholder - Add relevant DOIs if known for the specific variant
        # e.g., for the original DR splitting:
        # return ["10.1007/**********", "10.1137/080716542"]
        # For Easy-DR maybe "2009.12946" (related work)
        return ["not published yet"] # TODO: Publish appropriate DOI for FedExSplit / Easy-DR in FL context



@register_algorithm()
@add_docstring(
    Client.__doc__.replace(
        "The class to simulate the client node.",
        "Client node for the FedExSplit algorithm."
    ).replace("ClientConfig", "FedExSplitClientConfig")
)
class FedExSplitClient(Client):
    """
    Client node implementing the FedExSplit (Easy-DR based) algorithm steps.
    Highly optimized for memory: uses model parameters directly for local state
    and reuses buffers for intermediate calculations.
    """

    __name__ = "FedExSplitClient"

    def _post_init(self) -> None:
        """Initialize FedExSplit specific client state buffers."""
        super()._post_init()
        # No _local_parameters needed; self.model represents the state x_i^k at round start.

        # Initialize current beta and mu from config
        self._current_beta = self.config.beta
        self._current_mu = self.config.mu

        # Buffer to store intermediate e_k and v_k values.
        self._parameter_buffer: Optional[List[Parameter]] = None
        
        # Buffer for received global parameters (x_avg^k)
        self._parameter_global_buffer: Optional[List[Parameter]] = None

        # Buffer for gradients used not in e_k
        self._gradient_local_buffer: Optional[List[Parameter]] = None
        
        # Buffer for gradients used in e_k
        self._gradient_global_buffer: Optional[List[Parameter]] = None

    @property
    def required_config_fields(self) -> List[str]:
        """List of required fields in the client config for FedExSplit."""
        return ["beta", "gamma", "mu"]
    
    def pre_iter(self) -> None:
        """
        Instructions for different types
        ================================
        G1G2 & G2H2
        ----
        1. receive x_avg^k from server store in _parameter_buffer
        2. calculate local gradient g_i(x_avg^k), store in _gradient_local_buffer
        
        G1H1
        ----
        1. receive x_avg^k from server store in _parameter_buffer
        2. calculate local gradient g_i(x_i^k), store in _gradient_local_buffer
        """
        if "parameters" not in self._received_messages:
            raise RuntimeError(f"Client {self.client_id}: Not receiving parameters in pre_iter().")
        self._parameter_global_buffer = self._received_messages["parameters"]
        # --- Step 1: Compute g_i(x_avg^k) or g_i(x_i^k) ---
        # Compute gradient at the received global parameters (x_avg^k stored in buffer)
        # Result is stored directly into self._gradient_buffer
        if self.config.alg_type == "G1G2":
            # Compute G1 stored at _gradient_local_buffer
            self._gradient_local_buffer = self.compute_gradients(
                at=None, # Compute gradient at local state x_i^k
                dataloader=self.train_loader
            )
            # Compute G2 stored at _gradient_global_buffer
            self._gradient_global_buffer = self.compute_gradients(
                at=self._parameter_global_buffer, # Compute gradient at x_avg^k
                dataloader=self.train_loader
            )
        elif self.config.alg_type == "G2H2":
            # Compute G2 stored at _gradient_local_buffer
            self._gradient_local_buffer = self.compute_gradients(
                at=self._parameter_global_buffer, # Compute gradient at x_avg^k
                dataloader=self.train_loader
            )
        elif self.config.alg_type == "G1H1":
            # Compute G1 stored at _gradient_local_buffer
            self._gradient_local_buffer = self.compute_gradients(
                at=None, # Compute gradient at x_i^k
                dataloader=self.train_loader
            )
        else:
            raise ValueError(f"Unknown alg_type: {self.config.alg_type}")
        
    def accept_parameters(self) -> None:
        """
        Accepts parameters from received_messages.
        Need to be implemented in the child class to specify part in received_messages
        if the key is not "parameters".
        """
        # common communication is bypassed
        pass
    
    def communicate_for_gradient(self, target: "FedExSplitServer",) -> None:
        """
        Sends _gradient_buffer to server for aggration
        
        ---
        Args:
            target: The server to send the gradient to.
        """
        if self._gradient_local_buffer is None:
            raise ValueError(f"gradient_to_sent is None")
        target._received_messages_for_gradient.append(
            ClientMessage(
                client_id=self.client_id,
                gradient=self._gradient_local_buffer, # Send g_i(x_avg^k) from self._gradient_buffer
                train_samples=len(self.train_loader.dataset),
                metrics=None,
            )
        )
        target._num_communications += 1

    def communicate(self, target: "FedExSplitServer") -> None:
        """Sends the updated local model \\(x_i^{k+1}\\) (from self.model) to the server."""
        target._received_messages.append(
            ClientMessage(
                client_id=self.client_id,
                parameters=self.get_detached_model_parameters(), # Send x_i^{k+1} from self.model
                train_samples=len(self.train_loader.dataset),
                metrics=self._metrics,
            )
        )

    def update(self) -> None:
        """Performs the FedExSplit client update steps."""
        # Perform the local update calculations (calls self.train)
        # self.model currently holds x_i^k
        self.solve_inner() # Alias for self.train()
        # After this, self.model holds x_i^{k+1}


    def train(self) -> None:
        """
        Implements the core FedExSplit local update using memory optimization:
        - Compute e_k = x_i^k - x_avg^k + beta * g_i(x_avg^k) -> stored in _parameter_buffer
        - Compute g_i(x_i^k) -> stored in _gradient_buffer (overwritten)
        - Compute v_k = x_i^k + beta * g_i(x_i^k) - gamma * e_k -> stored in _parameter_buffer
        - Solve proximal subproblem using SGD starting from v_k to find x_i^{k+1} (updates self.model)
        """
        if self._parameter_global_buffer is None:
            raise RuntimeError(f"Client {self.client_id}: Parameter buffer not available in train().")
        if self.config.alg_type == "G1H1" or self.config.alg_type == "G2H2":
            # receive gradient H1 or H2 stored in _gradient_global_buffer
            if "gradient" not in self._received_messages:
                raise RuntimeError(f"Client {self.client_id}: Not receiving gradient in train().")
            self._gradient_global_buffer = self._received_messages["gradient"]
        self._parameter_buffer = []
        # --- Step 1: Compute e_k = x_i^k - x_avg^k + beta * g_i(x_avg^k) ---
        # Calculate in-place using self.model.parameters() (x_i^k),
        # _parameter_buffer (x_avg^k), and _gradient_buffer (g_i(x_avg^k)).
        # Result stored in _parameter_buffer.
        with torch.no_grad():
            # Use zip(self.model.parameters()) directly for x_i^k
            for model_p, avg_p, grad_p in zip(self.model.parameters(), self._parameter_global_buffer, self._gradient_global_buffer):
                # buf_p initially holds x_avg^k
                # Calculate e_k = model_p - avg_p + beta * grad_p and store in _parameter_buffer
                self._parameter_buffer.append((model_p - avg_p + self.config.beta * grad_p).detach())
                
        # Now _parameter_buffer holds e_k

        # --- Step 2: Compute v_k = x_i^k + beta * g_i(x_i^k) - gamma * e_k ---
        # Calculate in-place using self.model.parameters() (x_i^k),
        # _parameter_buffer (e_k), and _gradient_buffer (g_i(x_i^k)).
        # Result stored in _parameter_buffer.
        with torch.no_grad():
             # Use zip(self.model.parameters()) directly for x_i^k
            for buf_p, model_p, grad_p in zip(self._parameter_buffer, self.model.parameters(), self._gradient_local_buffer):
                # buf_p initially holds e_k
                # Calculate v_k = model_p + beta * grad_p - gamma * buf_p and store in buf_p
                buf_p.mul_(- self.config.gamma) # buf_p = - gamma * e_k
                buf_p.add_(model_p) # buf_p = - gamma * e_k + x_i^k
                buf_p.add_(self.config.beta * grad_p) # buf_p = - gamma * e_k + x_i^k + beta * g_i(x_i^k)
        # Now _parameter_buffer holds v_k

        # --- Step 3: Solve prox subproblem: x_i^{k+1} = argmin f_i(x) + (mu/2)||x - v_k||^2 ---
        # Load v_k (currently in _parameter_buffer) into the model to start SGD
        self.set_parameters(self._parameter_buffer) # self.model state is now v_k
        self.model.train()
        with tqdm(
            range(self.config.num_epochs),
            total=self.config.num_epochs,
            mininterval=1.0,
            disable=self.config.verbose < 2,
            leave=False,
        ) as pbar:
            for epoch in pbar:  # local update
                self.model.train()
                for X, y in self.train_loader:
                    X, y = X.to(self.device), y.to(self.device)
                    self.optimizer.zero_grad()
                    output = self.model(X)
                    loss = self.criterion(output, y)
                    loss.backward()
                    # Use ProxSGD optimizer for calculating proximal subproblem with SGD
                    self.optimizer.step(
                        local_weights=self._parameter_buffer,
                    )
                    # free memory
                    # del X, y, output, loss
        self.lr_scheduler.step()