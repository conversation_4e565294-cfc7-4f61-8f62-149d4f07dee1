"""
Implementation of the FedExSplit algorithm using the fl_sim.nodes base classes.

Based on the Easy-DR framework. Requires clients to maintain local state and compute gradients
at the global model state.
"""

import warnings
from typing import Any, Dict, List, Optional, Sequence

import torch
from torch import Tensor
from torch.nn.parameter import Parameter
from torch_ecg.utils.misc import add_docstring
from tqdm.auto import tqdm

# The following are relative imports for built-in algorithms
# from ...data_processing.fed_dataset import FedDataset
# from ...nodes import Client, ClientConfig, ClientMessage, Server, ServerConfig
# from .._misc import client_config_kw_doc, server_config_kw_doc
# from .._register import register_algorithm

# The following are absolute imports for custom algorithms
from fl_sim.data_processing.fed_dataset import FedDataset
from fl_sim.nodes import Client, ClientConfig, ClientMessage, Server, ServerConfig
from fl_sim.algorithms._misc import client_config_kw_doc, server_config_kw_doc
from fl_sim.algorithms._register import register_algorithm

__all__ = [
    "FedExSplitServer",
    "FedExSplitClient",
    "FedExSplitServerConfig",
    "FedExSplitClientConfig",
]


@register_algorithm()
@add_docstring(server_config_kw_doc, "append")
class FedExSplitServerConfig(ServerConfig):
    """
    Server configuration for the FedExSplit algorithm.

    Parameters
    ----------
    num_iters : int
        The number of communication rounds (outer iterations).
    num_clients : int
        The total number of clients.
    clients_sample_ratio : float
        The ratio of clients to sample for participation in each round.
    **kwargs : dict, optional
        Additional keyword arguments.
    """

    __name__ = "FedExSplitServerConfig"

    def __init__(
        self,
        num_iters: int,
        num_clients: int,
        clients_sample_ratio: float,
        **kwargs: Any,
    ) -> None:
        name = self.__name__.replace("ServerConfig", "")
        if kwargs.pop("algorithm", None) is not None:
            warnings.warn(
                f"The `algorithm` argument is fixed to `{name}` and will be ignored.",
                RuntimeWarning,
            )
        super().__init__(
            name,
            num_iters,
            num_clients,
            clients_sample_ratio,
            **kwargs,
        )


@register_algorithm()
@add_docstring(client_config_kw_doc, "append")
class FedExSplitClientConfig(ClientConfig):
    """
    Client configuration for the FedExSplit algorithm.

    Parameters
    ----------
    batch_size : int
        Batch size for local training (SGD for the proximal subproblem).
    num_epochs : int
        Number of local epochs to run SGD for solving the proximal subproblem.
    lr : float, default 1e-2
        Learning rate for the local SGD optimizer.
    beta : float, default 1.0
        Step size for the gradient term \\(g_i\\).
    gamma : float, default 1.0
        Step size for the error term \\(e_i\\).
    mu : float, default 1.0
        Coefficient for the proximal term \\(\\|x - v_i^k\\|^2\\).
    **kwargs : dict, optional
        Additional keyword arguments:
        - optimizer: Name of the optimizer for the proximal step (default "SGD").
    """

    __name__ = "FedExSplitClientConfig"

    def __init__(
        self,
        batch_size: int,
        num_epochs: int,
        lr: float = 1e-2,
        beta: float = 1e-2,
        gamma: float = 1.0,
        mu: float = 1e-2,
        **kwargs: Any,
    ) -> None:
        # Store FedExSplit specific parameters
        self.beta = beta
        self.gamma = gamma
        self.mu = mu

        name = self.__name__.replace("ClientConfig", "")
        if kwargs.pop("algorithm", None) is not None:
            warnings.warn(
                f"The `algorithm` argument is fixed to `{name}` and will be ignored.",
                RuntimeWarning,
            )
        # Default optimizer for the inner prox solve is SGD
        optimizer = "ProxSGD"
        if kwargs.pop("optimizer", None) is not None:
            warnings.warn(
                "The `optimizer` argument is fixed to `ProxSGD` and will be ignored.",
                RuntimeWarning,
            )

        super().__init__(
            name,
            optimizer,  # Optimizer used to solve the prox subproblem
            batch_size,
            num_epochs,
            lr,
            **kwargs,
        )


@register_algorithm()
@add_docstring(
    Server.__doc__.replace(
        "The class to simulate the server node.",
        "Server node for the FedExSplit algorithm."
    )
    .replace("ServerConfig", "FedExSplitServerConfig")
    .replace("ClientConfig", "FedExSplitClientConfig")
)
class FedExSplitServer(Server):
    """
    Server node for the FedExSplit (Easy-DR based) algorithm.
    Aggregates client models via simple averaging.
    """

    __name__ = "FedExSplitServer"
    
    # THIS METHOD IS OVERRIDDEN FROM THE BASE SERVER CLASS
    def train(self, mode: str = "federated", extra_configs: Optional[dict] = None) -> None:
        """
        Custom training method for FedExSplit.
        Always use federated training.
        """
        if self._complete_experiment:
            # reset before training if a previous experiment is completed
            self._reset()
        self._complete_experiment = False
        if mode.lower() != "federated":
            self._logger_manager.log_message(f"Training mode {mode} is not supported. Will use federated training.", 
                                             level=logging.INFO)
        self.train_federated(extra_configs)
        self._complete_experiment = True

    # THIS METHOD IS OVERRIDDEN FROM THE BASE SERVER CLASS
    def train_federated(self, extra_configs: Optional[dict] = None) -> None:
        """
        Full routine is:
        x^{k+1} = prox_{prox * f_i}(v_k)
        where:
        v_k = x^k + beta * g_i(x_i^k) - gamma * e_k
        e_k = x^k - \bar{x}^k + beta * u_k
        u_k is a variance reduction term tracking the gradient.
        Overridden federated training loop for FedExSplit. Each round consists of two stages:
        1. Compute the variance reduction term `u_k`.
        2. Perform local client updates using `u_k`.
        """
        if self._clients is None:
            self._setup_clients()

        if self._complete_experiment:
            self._reset()

        self._complete_experiment = False
        self._logger_manager.log_message("Starting FedExSplit federated training...")
        self.n_iter = 0

        # Initial report for multiprocessing progress
        if self._subprocess:
            self._report_progress(n_iter=0, num_iters=self.config.num_iters, training_phase="training")

        with tqdm(range(self.config.num_iters), total=self.config.num_iters, desc="FedExSplit Training", unit="round") as pbar:
            for self.n_iter in pbar:
                current_x_k = self.get_detached_model_parameters()

                # --- STAGE 1: COMPUTE u_k ---
                pbar.set_description(f"Round {self.n_iter+1} [Stage 1: Compute u_k]")
                
                # Decide which branch to take for computing u_k
                use_sync_branch = random.uniform(0, 1) < self.config.p

                # Sample clients for the main update in this round
                clients_for_update_ids = self._sample_clients()

                u_k: List[Tensor]
                if use_sync_branch:
                    self._logger_manager.log_message(f"Round {self.n_iter+1}: Computing u_k via SYNC.")
                    # Sample a new set of clients for gradient computation
                    clients_for_grad_ids = self._sample_clients()
                    u_k = self._get_gradients_from_clients(clients_for_grad_ids, at=current_x_k)
                else:
                    self._logger_manager.log_message(f"Round {self.n_iter+1}: Computing u_k via VR.")
                    # Reuse the main update clients to compute gradient differences
                    # This is a practical choice to avoid a third communication.
                    grad_diff = self._get_gradient_diffs_from_clients(clients_for_update_ids, current_x_k, self._model_prev)
                    
                    u_k = [
                        v_prev.to(self.device) + g_diff
                        for v_prev, g_diff in zip(self._v_current, grad_diff)
                    ]
                self._v_current = u_k

                # --- STAGE 2: CLIENT LOCAL UPDATES ---
                pbar.set_description(f"Round {self.n_iter+1} [Stage 2: Client Updates]")
                self._received_messages = []
                # Report progress for multiprocessing
                if self._subprocess:
                    client_progress_count = 0

                for client_id in tqdm(clients_for_update_ids, desc=f"Client Updates", leave=False):
                    client = self._clients[client_id]
                    
                    # Server sends x_k and the just-computed u_k
                    self._communicate(client)
                    # evaluate immediately after broadcasting
                    if self.n_iter > 0 and (self.n_iter + 1) % self.config.eval_every == 0:
                        for part in self.dataset.data_parts:
                            # NOTE: one should execute `client.evaluate`
                            # before `client._update`,
                            # otherwise the evaluation would be done
                            # on the ``personalized`` (locally fine-tuned) model.
                            metrics = client.evaluate(part)
                            self._logger_manager.log_metrics(
                                client_id,
                                metrics,
                                step=self.n_iter,
                                epoch=self.n_iter,
                                part=part,
                            )
                    # client trains the model
                    # and perhaps updates other local variables
                    client._update()  # Client solves its prox problem
                    # client communicates with server
                    # typically sending the local model
                    # and evaluated local metrics to the server
                    # and perhaps other local variables (e.g. gradients, etc.)
                    client._communicate(self) # Client sends back w_{k+1,m}

                    # Report progress for multiprocessing
                    if self._subprocess:
                        client_progress_count += 1
                        self._report_progress(
                            n_iter=self.n_iter + 1,
                            num_iters=self.config.num_iters,
                            current_client_progress=client_progress_count,
                            selected_clients_count=len(clients_for_update_ids),
                            training_phase="training"
                        )
                
                # Evaluation(aggregate client metrics) before server update
                if self.n_iter > 0 and (self.n_iter + 1) % self.config.eval_every == 0:
                    # server aggregates the metrics from clients
                    self.aggregate_client_metrics()
                
                # --- ROUND END: SERVER AGGREGATION AND STATE UPDATE ---
                # 0. Preparation for server update, this part is typically done in self._update()
                self._logger_manager.log_message("Server update...")
                if len(self._received_messages) == 0:
                    warnings.warn(
                        "No message received from the clients, unable to update server model.",
                        RuntimeWarning,
                    )
                    return
                assert all([isinstance(m, ClientMessage) for m in self._received_messages]), (
                    "received messages must be of type `ClientMessage`, "
                    f"but got {[type(m) for m in self._received_messages if not isinstance(m, ClientMessage)]}"
                )
                # 1. Aggregate client models (w_{k+1,m}) to get w_{k+1}
                # This updates self.model
                if len(self._received_messages) > 0:
                    self.avg_parameters(size_aware=True)
                
                # 2. Update server state for the next round
                self._model_prev = current_x_k  # x_k becomes w_{k-1} for the next round
                self._v_current = u_k          # u_k is stored for the next VR step
                
                # 3. Clear received messages
                self._received_messages = []
                    
                # no need for self._update() here since all updates are done in train_federated

        self._logger_manager.log_message("FedExSplit federated training finished.")
        self._logger_manager.flush()
        self._complete_experiment = True

    def _get_gradients_from_clients(self, client_ids: List[int], at: List[Tensor]) -> List[Tensor]:
        """Helper to get averaged gradients from a set of clients."""
        messages = []
        for client_id in client_ids:
            client = self._clients[client_id]
            grad = client.compute_gradients(at=at)
            self._num_communications += 2 # 2 messages per client sent for gradient computation
            messages.append({"grad": grad, "n_samples": len(client.train_loader.dataset)})
        
        avg_grad = [torch.zeros_like(p, device=self.device) for p in self.model.parameters()]
        total_samples = sum(m["n_samples"] for m in messages)
        if total_samples > 0:
            for m in messages:
                weight = m["n_samples"] / total_samples
                for p_avg, p_grad in zip(avg_grad, m["grad"]):
                    p_avg.add_(p_grad.to(self.device), alpha=weight)
        return avg_grad

    def _get_gradient_diffs_from_clients(self, client_ids: List[int], x_k: List[Tensor], x_k_prev: List[Tensor]) -> List[Tensor]:
        """Helper to get averaged gradient differences from a set of clients."""
        messages = []
        for client_id in client_ids:
            client = self._clients[client_id]
            grad_k = client.compute_gradients(at=x_k)
            grad_k_prev = client.compute_gradients(at=x_k_prev)
            grad_diff = [g1 - g2 for g1, g2 in zip(grad_k, grad_k_prev)]
            self._num_communications += 3 # 3 messages per client sent for gradient computation
            messages.append({"grad_diff": grad_diff, "n_samples": len(client.train_loader.dataset)})

        avg_grad_diff = [torch.zeros_like(p, device=self.device) for p in self.model.parameters()]
        total_samples = sum(m["n_samples"] for m in messages)
        if total_samples > 0:
            for m in messages:
                weight = m["n_samples"] / total_samples
                for p_avg_diff, p_diff in zip(avg_grad_diff, m["grad_diff"]):
                    p_avg_diff.add_(p_diff.to(self.device), alpha=weight)
        return avg_grad_diff

    @property
    def required_config_fields(self) -> List[str]:
        """No extra server-specific config fields needed for basic FedExSplit."""
        return []

    @property
    def client_cls(self) -> type:
        """The class of the client node."""
        return FedExSplitClient
    
    def _communicate(self, target: "Client", mode: str = "param_only") -> None:
        """Broadcast to target client, and maintain state variables."""
        if mode.lower() == "param_only":
            self.communicate_param_only(target)
        elif mode.lower() == "param_and_prev":
            self.communicate_param_and_prev(target)
        elif mode.lower() == "param_gradtracker":
            self.communicate_param_gradtracker(target)
        else:
            raise ValueError(f"Unknown communication mode: {mode}")
        self._num_communications += 1

    def communicate_param_only(self, target: "FedExSplitClient") -> None:
        """Sends the current global model parameters to the target client."""
        target._received_messages = {"parameters": self.get_detached_model_parameters()}
        
    def communicate_param_and_prev(self, target: "FedExSplitClient") -> None:
        """Sends the current global model parameters and previous model parameters to the target client."""
        target._received_messages = {
            "parameters": self.get_detached_model_parameters(),
            "parameters_prev": self._model_prev,
            }
    
    def communicate_param_gradtracker(self, target: "FedExSplitClient") -> None:
        """Sends the current global model parameters and gradient tracker to the target client."""
        target._received_messages = {
            "parameters": self.get_detached_model_parameters(),
            "gradient_tracker": self._gradient_tracker,
            }

    def update(self) -> None:
        """Aggregates the received client models using simple averaging."""
        # FedExSplit aggregates the updated local models x_i^{k+1}
        self.avg_parameters(size_aware=False) # \bar{x}^{k+1} = (1/N) * sum(x_i^{k+1})

    @property
    def config_cls(self) -> Dict[str, type]:
        """Returns the configuration classes for server and client."""
        return {
            "server": FedExSplitServerConfig,
            "client": FedExSplitClientConfig,
        }

    @property
    def doi(self) -> List[str]:
        """DOI(s) related to the Easy-DR or similar methods if available."""
        # Placeholder - Add relevant DOIs if known for the specific variant
        # e.g., for the original DR splitting:
        # return ["10.1007/BF01431806", "10.1137/080716542"]
        # For Easy-DR maybe "2009.12946" (related work)
        return ["not published yet"] # TODO: Publish appropriate DOI for FedExSplit / Easy-DR in FL context



@register_algorithm()
@add_docstring(
    Client.__doc__.replace(
        "The class to simulate the client node.",
        "Client node for the FedExSplit algorithm."
    ).replace("ClientConfig", "FedExSplitClientConfig")
)
class FedExSplitClient(Client):
    """
    Client node implementing the FedExSplit (Easy-DR based) algorithm steps.
    Highly optimized for memory: uses model parameters directly for local state
    and reuses buffers for intermediate calculations.
    """

    __name__ = "FedExSplitClient"

    def _post_init(self) -> None:
        """Initialize FedExSplit specific client state buffers."""
        super()._post_init()
        # No _local_parameters needed; self.model represents the state x_i^k at round start.

        # Initialize current beta and mu from config
        self._current_beta = self.config.beta
        self._current_mu = self.config.mu

        # Buffer for received global parameters (\bar{x}^k)
        # Also reused to store intermediate e_k and v_k values.
        self._parameter_buffer: Optional[List[Parameter]] = None

        # Buffer for gradients (reused for g_i(\bar{x}^k) and g_i(x_i^k))
        self._gradient_buffer: List[Tensor] = [
            torch.zeros_like(p.data, device=self.device) for p in self.model.parameters()
        ] # Ensure buffer is on the correct device from the start

        # DataLoader for computing gradient at the global model state (\bar{x}^k)
        self.global_grad_loader = self.train_loader

    @property
    def required_config_fields(self) -> List[str]:
        """List of required fields in the client config for FedExSplit."""
        return ["beta", "gamma", "mu"]

    def communicate(self, target: "FedExSplitServer") -> None:
        """Sends the updated local model \\(x_i^{k+1}\\) (from self.model) to the server."""
        target._received_messages.append(
            ClientMessage(
                client_id=self.client_id,
                parameters=self.get_detached_model_parameters(), # Send x_i^{k+1} from self.model
                train_samples=len(self.train_loader.dataset),
                metrics=self._metrics,
            )
        )

    def update(self) -> None:
        """Performs the FedExSplit client update steps."""
        # 1. Receive global model \bar{x}^k from server and store in buffer
        try:
            # Store received parameters directly into the buffer
            self._parameter_buffer = [p.detach().clone().to(self.device) for p in self._received_messages["parameters"]]
        except KeyError:
            warnings.warn(
                f"Client {self.client_id}: No parameters received from server. "
                "Cannot perform FedExSplit update. Skipping round.",
                RuntimeWarning,
            )
            return # Skip update if no global model received
        except Exception as err:
            raise err

        # 2. Perform the local update calculations (calls self.train)
        # self.model currently holds x_i^k
        self.solve_inner() # Alias for self.train()
        # After this, self.model holds x_i^{k+1}


    def train(self) -> None:
        """
        Implements the core FedExSplit local update using memory optimization:
        - Compute g_i(\bar{x}^k) -> stored in _gradient_buffer
        - Compute e_k = x_i^k - \bar{x}^k + beta * g_i(\bar{x}^k) -> stored in _parameter_buffer
        - Compute g_i(x_i^k) -> stored in _gradient_buffer (overwritten)
        - Compute v_k = x_i^k + beta * g_i(x_i^k) - gamma * e_k -> stored in _parameter_buffer
        - Solve proximal subproblem using SGD starting from v_k to find x_i^{k+1} (updates self.model)
        """
        if self._parameter_buffer is None:
            warnings.warn(f"Client {self.client_id}: Parameter buffer not available in train(). Skipping.", RuntimeWarning)
            return

        # --- Step 1: Compute g_i(\bar{x}^k) ---
        # Compute gradient at the received global parameters (\bar{x}^k stored in buffer)
        # Result is stored directly into self._gradient_buffer
        self._gradient_buffer = self.compute_gradients(
            at=self._parameter_buffer, # Compute gradient at \bar{x}^k
            dataloader=self.global_grad_loader
        )

        # --- Step 2: Compute e_k = x_i^k - \bar{x}^k + beta * g_i(\bar{x}^k) ---
        # Calculate in-place using self.model.parameters() (x_i^k),
        # _parameter_buffer (\bar{x}^k), and _gradient_buffer (g_i(\bar{x}^k)).
        # Result stored in _parameter_buffer.
        with torch.no_grad():
            # Use zip(self.model.parameters()) directly for x_i^k
            for buf_p, model_p, grad_p in zip(self._parameter_buffer, self.model.parameters(), self._gradient_buffer):
                # buf_p initially holds \bar{x}^k
                # Calculate e_k = model_p - buf_p + beta * grad_p and store in buf_p
                buf_p.mul_(-1)            # buf_p = - \bar{x}^k
                buf_p.add_(model_p.data)  # buf_p = x_i^k - \bar{x}^k
                buf_p.add_(grad_p, alpha=self._current_beta) # buf_p = x_i^k - \bar{x}^k + beta * g_i(\bar{x}^k) = e_k
        # Now _parameter_buffer holds e_k

        # --- Step 3: Compute g_i(x_i^k) ---
        # Compute gradient at the current model parameters (x_i^k)
        # Result overwrites self._gradient_buffer
        self._gradient_buffer = self.compute_gradients(
            at=self.model.parameters(), # Compute gradient at local state x_i^k
            dataloader=self.train_loader
        )

        # --- Step 4: Compute v_k = x_i^k + beta * g_i(x_i^k) - gamma * e_k ---
        # Calculate in-place using self.model.parameters() (x_i^k),
        # _parameter_buffer (e_k), and _gradient_buffer (g_i(x_i^k)).
        # Result stored in _parameter_buffer.
        with torch.no_grad():
             # Use zip(self.model.parameters()) directly for x_i^k
            for buf_p, model_p, grad_p in zip(self._parameter_buffer, self.model.parameters(), self._gradient_buffer):
                # buf_p initially holds e_k
                # Calculate v_k = model_p + beta * grad_p - gamma * buf_p and store in buf_p
                buf_p.mul_(-self.config.gamma) # buf_p = -gamma * e_k
                buf_p.add_(model_p.data)       # buf_p = x_i^k - gamma * e_k
                buf_p.add_(grad_p, alpha=self._current_beta) # buf_p = x_i^k + beta * g_i(x_i^k) - gamma * e_k = v_k
        # Now _parameter_buffer holds v_k

        # --- Step 5: Solve prox subproblem: x_i^{k+1} = argmin f_i(x) + (mu/2)||x - v_k||^2 ---
        # Load v_k (currently in _parameter_buffer) into the model to start SGD
        self.set_parameters(self._parameter_buffer) # self.model state is now v_k
        self.model.train()
        with tqdm(
            range(self.config.num_epochs),
            total=self.config.num_epochs,
            mininterval=1.0,
            disable=self.config.verbose < 2,
            leave=False,
        ) as pbar:
            for epoch in pbar:  # local update
                self.model.train()
                for X, y in self.train_loader:
                    X, y = X.to(self.device), y.to(self.device)
                    self.optimizer.zero_grad()
                    output = self.model(X)
                    loss = self.criterion(output, y)
                    loss.backward()
                    # Use ProxSGD optimizer for calculating proximal subproblem with SGD
                    self.optimizer.step(
                        local_weights=self._cached_parameters,
                    )
                    # free memory
                    # del X, y, output, loss
        self.lr_scheduler.step()