import torch
from tqdm.notebook import tqdm
from torch.utils.data import DataLoader
from ..models import SimpleMLP
from .base import FedAlgorithm

class FedAvgClient:
    """FedAvg算法的客户端实现"""
    def __init__(self, client_id, dataset, config):
        self.id = client_id
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() and config.use_cuda else "cpu")
        self.model = SimpleMLP(config.input_size, config.output_size)
        self.optimizer = None
        self.train_loader = DataLoader(dataset, batch_size=config.batch_size, shuffle=True)
    
    def train(self, global_model_state):
        """本地训练"""
        self.model.load_state_dict(global_model_state)
        self.model.to(self.device)
        self.model.train()
        
        self.optimizer = torch.optim.SGD(self.model.parameters(), lr=self.config.learning_rate)
        
        for _ in range(self.config.local_epochs):
            for data, labels in self.train_loader:
                data, labels = data.to(self.device), labels.to(self.device)
                self.optimizer.zero_grad()
                outputs = self.model(data)
                loss = torch.nn.functional.cross_entropy(outputs, labels)
                loss.backward()
                self.optimizer.step()
        
        return self.model.state_dict()

class FedAvgServer:
    """FedAvg算法的服务器实现"""
    def __init__(self, config, test_data):
        self.config = config
        self.global_model = SimpleMLP(config.input_size, config.output_size)
        self.test_loader = DataLoader(test_data, batch_size=config.batch_size, shuffle=False)
        self.device = torch.device("cuda" if torch.cuda.is_available() and config.use_cuda else "cpu")
        self.global_model.to(self.device)
    
    def aggregate(self, client_updates):
        """FedAvg聚合算法"""
        if not client_updates:
            return
        averaged_state = {}
        for key in client_updates[0].keys():
            params = torch.stack([update[key].to(self.device) for update in client_updates])
            averaged_state[key] = torch.mean(params, dim=0)
        self.global_model.load_state_dict(averaged_state)
        
    def evaluate(self):
        """在测试集上评估模型"""
        self.global_model.eval()
        correct, total = 0, 0
        with torch.no_grad():
            for data, labels in self.test_loader:
                data = data.to(self.device)
                labels = labels.to(self.device)
                outputs = self.global_model(data)
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        return correct / total

class FedAvg(FedAlgorithm):
    """FedAvg算法实现"""
    def init_clients(self, client_datasets):
        """初始化客户端"""
        self.clients = [
            FedAvgClient(i, client_datasets[i], self.config) 
            for i in range(self.config.num_clients)
        ]
    
    def init_server(self, test_data):
        """初始化服务器"""
        self.server = FedAvgServer(self.config, test_data)

    def train(self):
        """训练"""
        accuracy_history = []
        with tqdm(total=self.config.comm_rounds, desc="🦉 联邦学习进度", unit="round") as pbar:
            for _ in range(self.config.comm_rounds):
                # 客户端更新
                client_updates = []
                for client in tqdm(self.clients, desc="🚀 客户端更新", leave=False, unit="client"):
                    updated_model = client.train(self.server.global_model.state_dict())
                    client_updates.append(updated_model)
                    
                # 聚合客户端更新
                self.aggregate(client_updates)
                
                # 模型评估
                accuracy = self.evaluate()
                accuracy_history.append(accuracy)
                
                # 更新进度条显示
                pbar.set_postfix({
                    "准确率": f"{accuracy:.2%}",
                    "设备": "GPU✅" if torch.cuda.is_available() else "CPU⚠️"
                })
                pbar.update(1)
        return accuracy_history
        
    def aggregate(self, updates):
        """聚合客户端更新"""
        self.server.aggregate(updates)
        
    def evaluate(self):
        """评估当前模型"""
        return self.server.evaluate()