from abc import ABC, abstractmethod
from typing import List, Dict, Optional
import torch
from tqdm.notebook import tqdm

class FedAlgorithm(ABC):
    """联邦学习算法基类（所有具体算法必须继承此类）"""
    
    def __init__(self, config):
        self.config = config
        self.accuracy_history: List[float] = []
        self.clients: Optional[List] = None
        self.server: Optional[object] = None
        
    @abstractmethod
    def init_clients(self, client_datasets) -> None:
        """初始化客户端实例"""
        pass
        
    @abstractmethod
    def init_server(self, test_data) -> None:
        """初始化服务器实例"""
        pass
        
    @abstractmethod
    def train(self) -> List[Dict]:
        """执行训练"""
        pass
        
    @abstractmethod
    def aggregate(self, updates: List[Dict]) -> None:
        """聚合客户端参数更新"""
        pass
        
    @abstractmethod
    def evaluate(self) -> float:
        """评估当前全局模型性能"""
        pass

    def train(self, client_datasets, test_data, verbose=True) -> List[float]:
        """完整的训练流程控制器
        
        Args:
            client_datasets: 各客户端数据集列表
            test_data: 全局测试数据集
            verbose: 是否显示进度条
            
        Returns:
            每轮训练后的准确率历史记录
        """
        # 初始化组件
        self.init_clients(client_datasets)
        self.init_server(test_data)
        
        # 训练循环
        with tqdm(total=self.config.comm_rounds, 
                 desc="🚀 联邦训练进度", 
                 disable=not verbose) as pbar:
            
            for _ in range(self.config.comm_rounds):
                updates = self.train_one_round()
                self.aggregate(updates)
                accuracy = self.evaluate()
                self.accuracy_history.append(accuracy)
                
                pbar.set_postfix({
                    "acc": f"{accuracy:.2%}",
                    "device": "GPU" if torch.cuda.is_available() else "CPU"
                })
                pbar.update(1)
        
        return self.accuracy_history