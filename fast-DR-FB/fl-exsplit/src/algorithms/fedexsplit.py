import torch
from torch.utils.data import DataLoader
from tqdm.notebook import tqdm
from ..models import SimpleMLP
from .base import FedAlgorithm

class FedExSplitClient:
    def __init__(self, client_id, dataset, config):
        self.id = client_id
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() and config.use_cuda else "cpu")
        self.model = SimpleMLP(config.input_size, config.output_size).to(self.device)
        self.optimizer = torch.optim.SGD(self.model.parameters(), lr=config.learning_rate)
        self.train_loader = DataLoader(dataset, batch_size=config.batch_size, shuffle=True)
        self.beta = config.beta    # 梯度步长参数
        self.gamma = config.gamma  # 外推参数
        self.mu = config.mu        # 近端项系数
        self.beta_decay = config.beta_decay  # 衰减因子
        self.mu_decay = config.mu_decay
        # 初始化梯度
        self.grad = self.compute_gradient()

    def compute_gradient(self):
        """计算本地梯度 g_i(x_i^k) = \nabla f_i(x_i^k)"""
        self.model.train()
        self.optimizer.zero_grad()
        
        # 使用全部数据计算梯度，但按batch大小累计
        num_samples = 0
        total_samples = len(self.train_loader.dataset)
        
        grad = {name: torch.zeros_like(param) for name, param in self.model.named_parameters()}
        
        for data, labels in self.train_loader:
            data, labels = data.to(self.device), labels.to(self.device)
            batch_size = data.size(0)
            num_samples += batch_size
            
            outputs = self.model(data)
            loss = torch.nn.functional.cross_entropy(outputs, labels)
            loss.backward()
            
            # 累计梯度，按批次大小加权
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    grad[name] += param.grad.clone() * (batch_size / total_samples)
            
            self.optimizer.zero_grad()  # 清除当前批次的梯度
        
        return grad

    def compute_e_k(self, bar_x_k):
        """计算 e_i^k = x_i^k - \bar{x}^k"""
        e_k = {}
        self.model.load_state_dict(bar_x_k)
        self.grad = self.compute_gradient()
        for name, param in self.model.state_dict().items():
            e_k[name] = param + self.beta * self.grad[name] - bar_x_k[name].to(self.device)
        return e_k

    def compute_v_k(self, e_k, grad):
        """计算 v_i^k = x_i^k + \beta g_i(x_i^k) - \gamma e_i^k"""
        v_k = {}
        for name, param in self.model.state_dict().items():
            v_k[name] = param + self.beta * grad[name] - self.gamma * e_k[name]
        return v_k

    def prox_update(self, v_k):
        """解决近端子问题 x_i^{k+1} = argmin_x f_i(x) + (mu/2) ||x - v_k||^2"""
        self.model.load_state_dict(v_k)
        self.model.train()
        for _ in range(self.config.local_epochs):
            for data, labels in self.train_loader:
                data, labels = data.to(self.device), labels.to(self.device)
                self.optimizer.zero_grad()
                outputs = self.model(data)
                loss = torch.nn.functional.cross_entropy(outputs, labels)
                # 添加近端项
                for name, param in self.model.named_parameters():
                    loss += (self.mu / (2)) * (param - v_k[name].to(self.device)).pow(2).sum()
                loss.backward()
                self.optimizer.step()
        return self.model.state_dict()

    def train(self, bar_x_k):
        """客户端更新步骤"""
        # 计算 e_i^k
        e_k = self.compute_e_k(bar_x_k)
        # 计算 v_i^k
        v_k = self.compute_v_k(e_k, self.grad)
        # 执行近端更新
        updated_model = self.prox_update(v_k)
        self.beta = self.beta * self.beta_decay
        self.mu = self.mu * self.mu_decay
        return updated_model
        
        # self.prox_update(bar_x_k)
        # return self.model.state_dict()

class FedExSplitServer:
    def __init__(self, config, test_data):
        self.config = config
        self.global_model = SimpleMLP(config.input_size, config.output_size)
        self.test_loader = DataLoader(test_data, batch_size=config.batch_size, shuffle=False)
        self.device = torch.device("cuda" if torch.cuda.is_available() and config.use_cuda else "cpu")
        self.global_model.to(self.device)

    def aggregate(self, client_updates):
        """聚合客户端模型以更新全局模型 \bar{x}^k"""
        if not client_updates:
            return
        averaged_state = {}
        for key in client_updates[0].keys():
            params = torch.stack([update[key].to(self.device) for update in client_updates])
            averaged_state[key] = torch.mean(params, dim=0)
        self.global_model.load_state_dict(averaged_state)
        
    def evaluate(self):
        """在测试集上评估全局模型 \bar{x}^k"""
        self.global_model.eval()
        correct, total = 0, 0
        with torch.no_grad():
            for data, labels in self.test_loader:
                data = data.to(self.device)
                labels = labels.to(self.device)
                outputs = self.global_model(data)
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        return correct / total

    def get_global_model(self):
        """返回当前全局模型参数 \bar{x}^k"""
        return self.global_model.state_dict()

class FedExSplit(FedAlgorithm):
    def init_clients(self, client_datasets):
        self.clients = [
            FedExSplitClient(i, client_datasets[i], self.config)
            for i in range(self.config.num_clients)
        ]
    
    def init_server(self, test_data):
        self.server = FedExSplitServer(self.config, test_data)

    def train(self):
        accuracy_history = []
        with tqdm(total=self.config.comm_rounds, desc="🦉 联邦学习进度", unit="round") as pbar:
            for _ in range(self.config.comm_rounds):
                # 客户端更新
                client_updates = []
                for client in tqdm(self.clients, desc="🚀 客户端更新", leave=False, unit="client"):
                    updated_model = client.train(self.server.global_model.state_dict())
                    client_updates.append(updated_model)
                    
                # 聚合客户端更新
                self.aggregate(client_updates)
                
                # 模型评估
                accuracy = self.evaluate()
                accuracy_history.append(accuracy)
                
                # 更新进度条显示
                pbar.set_postfix({
                    "准确率": f"{accuracy:.2%}",
                    "设备": "GPU✅" if torch.cuda.is_available() else "CPU⚠️"
                })
                pbar.update(1)
        return accuracy_history

    def aggregate(self, updates):
        """聚合客户端模型以更新全局模型 \bar{x}^k"""
        self.server.aggregate(updates)
        
    def evaluate(self):
        """在测试集上评估全局模型 \bar{x}^k"""
        return self.server.evaluate()