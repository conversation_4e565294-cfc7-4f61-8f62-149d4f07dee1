from torchvision import datasets, transforms
from torch.utils.data import Subset
import torch

def prepare_mnist_full(data_root, config):
    """准备MNIST数据集并划分客户端数据（IID）"""
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,)),
        transforms.Lambda(lambda x: x.view(-1))  # 展平为784维向量
    ])
    
    # 下载数据集
    train_data = datasets.MNIST(
        root=data_root,
        train=True,
        download=True,
        transform=transform
    )
    
    test_data = datasets.MNIST(
        root=data_root,
        train=False,
        download=True,
        transform=transform
    )
    
    # 划分客户端数据
    client_datasets = []
    num_samples = len(train_data) // config.num_clients
    indices = torch.randperm(len(train_data)).tolist()
    
    for i in range(config.num_clients):
        client_indices = indices[i*num_samples : (i+1)*num_samples]
        client_datasets.append(Subset(train_data, client_indices))
    
    return client_datasets, test_data