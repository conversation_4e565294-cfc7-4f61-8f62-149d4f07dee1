{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 联邦学习完整演示"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: /home/<USER>/federated-learning/fast-DR-FB/fl-exsplit\n", "数据存储路径: /home/<USER>/federated-learning/fast-DR-FB/fl-exsplit/data\n", "✅ 所有模块导入成功！\n"]}], "source": ["# 初始化设置\n", "%matplotlib inline\n", "import os\n", "import sys\n", "import torch\n", "import copy\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# 项目根目录配置\n", "ROOT_DIR = \"fl-exsplit\"  # 根目录名称\n", "DATA_DIR = os.path.join(os.getcwd(), \"data\")  # 数据路径\n", "\n", "# 自动路径修正\n", "if os.path.basename(os.getcwd()) != ROOT_DIR:\n", "    base_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "    if os.path.basename(base_path) == ROOT_DIR:\n", "        os.chdir(base_path)\n", "    else:\n", "        raise FileNotFoundError(f\"请确保Notebook在{ROOT_DIR}目录下运行\")\n", "\n", "sys.path.insert(0, '.')  # 添加项目根目录到路径\n", "\n", "# 设置中文字体（根据系统选择）\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows\n", "# plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # macOS\n", "# plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']  # Linux\n", "\n", "# 解决负号显示问题\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"数据存储路径: {DATA_DIR}\")\n", "\n", "# 验证模块导入\n", "try:\n", "    from configs import params\n", "    from src.data_loader import prepare_mnist_full\n", "    from src.models import SimpleMLP\n", "    from src.algorithms import FedAvg, FedExSplit\n", "    print(\"✅ 所有模块导入成功！\")\n", "except ImportError as e:\n", "    print(f\"❌ 导入失败: {e}\")\n", "    raise"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch>=2.6.0+cu118\n", "torchvision>=0.21.0+cu118\n", "matplotlib>=3.10.1\n", "tqdm>=4.67.1\n"]}], "source": ["# 打印所有第三方库的版本\n", "import torch\n", "import torchvision\n", "import matplotlib\n", "import tqdm\n", "\n", "print(\"torch>={}\".format(torch.__version__))\n", "print(\"torchvision>={}\".format(torchvision.__version__))\n", "print(\"matplotlib>={}\".format(matplotlib.__version__))\n", "print(\"tqdm>={}\".format(tqdm.__version__))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 设备配置与数据准备"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总数据规模统计：\n", "- 客户端总数: 10\n", "- 客户端 1: 6000 个样本\n", "- 客户端 2: 6000 个样本\n", "- 客户端 3: 6000 个样本\n", "- 客户端 4: 6000 个样本\n", "- 客户端 5: 6000 个样本\n", "- 客户端 6: 6000 个样本\n", "- 客户端 7: 6000 个样本\n", "- 客户端 8: 6000 个样本\n", "- 客户端 9: 6000 个样本\n", "- 客户端 10: 6000 个样本\n", "- 测试集规模: 10000 个样本\n", "- 训练总样本数: 60000 个样本\n", "- 总数据规模: 70000 个样本\n"]}], "source": ["# 初始化配置\n", "config = params.config\n", "\n", "# 准备数据\n", "client_datasets, test_data = prepare_mnist_full(data_root=\"./data\", config=config)\n", "\n", "# 打印数据规模信息\n", "print(f\"总数据规模统计：\")\n", "print(f\"- 客户端总数: {config.num_clients}\")\n", "\n", "# 打印每个客户端数据规模\n", "total_train_samples = 0\n", "for i, dataset in enumerate(client_datasets):\n", "    client_size = len(dataset)\n", "    total_train_samples += client_size\n", "    print(f\"- 客户端 {i+1}: {client_size} 个样本\")\n", "\n", "# 打印测试集规模\n", "test_size = len(test_data)\n", "print(f\"- 测试集规模: {test_size} 个样本\")\n", "print(f\"- 训练总样本数: {total_train_samples} 个样本\")\n", "print(f\"- 总数据规模: {total_train_samples + test_size} 个样本\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 初始化算法"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 创建FedAvg算法实例\n", "fed_avg = FedAvg(config)\n", "\n", "# 初始化服务端和客户端\n", "fed_avg.init_server(test_data)\n", "fed_avg.init_clients(client_datasets)\n", "\n", "# 创建FedExSplit算法实例\n", "fed_exsplit = FedExSplit(config)\n", "\n", "# 初始化服务端和客户端\n", "fed_exsplit.init_server(test_data)\n", "fed_exsplit.init_clients(client_datasets)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 联邦学习训练循环"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'fed_avg_accuracy_history' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      5\u001b[39m os.makedirs(save_dir, exist_ok=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# 保存FedAvg模型\u001b[39;00m\n\u001b[32m      8\u001b[39m torch.save({\n\u001b[32m      9\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mmodel_state_dict\u001b[39m\u001b[33m'\u001b[39m: fed_avg.server.global_model.state_dict(),\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m     \u001b[33m'\u001b[39m\u001b[33maccuracy_history\u001b[39m\u001b[33m'\u001b[39m: \u001b[43mfed_avg_accuracy_history\u001b[49m,\n\u001b[32m     11\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mconfig\u001b[39m\u001b[33m'\u001b[39m: config\n\u001b[32m     12\u001b[39m }, os.path.join(save_dir, \u001b[33m'\u001b[39m\u001b[33mfedavg_model.pt\u001b[39m\u001b[33m'\u001b[39m))\n\u001b[32m     13\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFedAvg模型已保存到 \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mos.path.join(save_dir,\u001b[38;5;250m \u001b[39m\u001b[33m'\u001b[39m\u001b[33mfedavg_model.pt\u001b[39m\u001b[33m'\u001b[39m)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     15\u001b[39m \u001b[38;5;66;03m# 保存FedExSplit模型\u001b[39;00m\n", "\u001b[31mNameError\u001b[39m: name 'fed_avg_accuracy_history' is not defined"]}], "source": ["# 保存模型\n", "# 创建保存目录\n", "import os\n", "save_dir = \"saved_models\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "# 保存FedAvg模型\n", "torch.save({\n", "    'model_state_dict': fed_avg.server.global_model.state_dict(),\n", "    'accuracy_history': fed_avg_accuracy_history,\n", "    'config': config\n", "}, os.path.join(save_dir, 'fedavg_model.pt'))\n", "print(f\"FedAvg模型已保存到 {os.path.join(save_dir, 'fedavg_model.pt')}\")\n", "\n", "# 保存FedExSplit模型\n", "torch.save({\n", "    'model_state_dict': fed_exsplit.server.global_model.state_dict(),\n", "    'accuracy_history': fed_exsplit_accuracy_history,\n", "    'config': config\n", "}, os.path.join(save_dir, 'fedexsplit_model.pt'))\n", "print(f\"FedExSplit模型已保存到 {os.path.join(save_dir, 'fedexsplit_model.pt')}\")\n", "\n", "# 保存准确率历史记录为CSV文件，方便后续分析\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# 创建DataFrame并保存\n", "results_df = pd.DataFrame({\n", "    'round': np.arange(1, len(fed_avg_accuracy_history) + 1),\n", "    'fedavg_accuracy': fed_avg_accuracy_history,\n", "    'fedexsplit_accuracy': fed_exsplit_accuracy_history\n", "})\n", "results_df.to_csv(os.path.join(save_dir, 'accuracy_history.csv'), index=False)\n", "print(f\"准确率历史记录已保存到 {os.path.join(save_dir, 'accuracy_history.csv')}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 绘图"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'fed_avg_accuracy_history' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 可视化两种算法的准确率对比\u001b[39;00m\n\u001b[32m      2\u001b[39m plt.figure(figsize=(\u001b[32m12\u001b[39m, \u001b[32m6\u001b[39m))\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m plt.plot(\u001b[43mfed_avg_accuracy_history\u001b[49m, \u001b[33m'\u001b[39m\u001b[33mbo-\u001b[39m\u001b[33m'\u001b[39m, linewidth=\u001b[32m2\u001b[39m, markersize=\u001b[32m8\u001b[39m, label=\u001b[33m'\u001b[39m\u001b[33mFedAvg\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      4\u001b[39m plt.plot(fed_exsplit_accuracy_history, \u001b[33m'\u001b[39m\u001b[33mro-\u001b[39m\u001b[33m'\u001b[39m, linewidth=\u001b[32m2\u001b[39m, markersize=\u001b[32m8\u001b[39m, label=\u001b[33m'\u001b[39m\u001b[33mFedExSplit\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      5\u001b[39m plt.xlabel(\u001b[33m\"\u001b[39m\u001b[33mCommunication Rounds\u001b[39m\u001b[33m\"\u001b[39m, fontsize=\u001b[32m12\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'fed_avg_accuracy_history' is not defined"]}, {"data": {"text/plain": ["<Figure size 1200x600 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化两种算法的准确率对比\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(fed_avg_accuracy_history, 'bo-', linewidth=2, markersize=8, label='FedAvg')\n", "plt.plot(fed_exsplit_accuracy_history, 'ro-', linewidth=2, markersize=8, label='FedExSplit')\n", "plt.xlabel(\"Communication Rounds\", fontsize=12)\n", "plt.ylabel(\"Test Accuracy\", fontsize=12)\n", "plt.title(\"Performance Comparison: FedAvg vs FedExSplit\", fontsize=14, pad=20)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(range(config.comm_rounds))\n", "plt.legend(fontsize=12)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def test_single_image(model, image, label, model_name=\"\"):\n", "    \"\"\"测试单个图像的识别\"\"\"\n", "    model.eval()\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    image = image.to(device)\n", "    \n", "    with torch.no_grad():\n", "        output = model(image)\n", "        probs = torch.softmax(output, dim=1).squeeze()\n", "        pred = probs.argmax().item()\n", "    \n", "    plt.figure(figsize=(6, 3))\n", "    plt.subplot(1, 2, 1)\n", "    plt.imshow(image.cpu().squeeze().numpy().reshape(28, 28), cmap='gray')\n", "    plt.title(f\"真实标签: {label}\")\n", "    plt.axis('off')\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    plt.barh(range(len(probs)), probs.cpu().numpy(), color='blue')\n", "    plt.yticks(range(len(probs)), [str(i) for i in range(len(probs))])\n", "    plt.xlabel(\"Probability\")\n", "    plt.title(f\"{model_name}预测结果: {pred}\")\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_idx = 4112  # 可以修改这个索引\n", "sample_image, sample_label = test_data[sample_idx]\n", "sample_image = sample_image.unsqueeze(0)  # 添加batch维度\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "untrained_model = SimpleMLP(config.input_size, config.output_size).to(device)\n", "test_single_image(untrained_model, sample_image, sample_label, \"未训练模型\")\n", "test_single_image(fed_exsplit.server.global_model, sample_image, sample_label, \"训练后模型\")"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}