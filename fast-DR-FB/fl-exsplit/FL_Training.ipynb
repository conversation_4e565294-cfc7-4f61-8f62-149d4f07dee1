{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 联邦学习完整演示"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: c:\\Users\\<USER>\\OneDrive\\paper\\Fast-FB-DR\\fl-exsplit\n", "数据存储路径: c:\\Users\\<USER>\\OneDrive\\paper\\Fast-FB-DR\\fl-exsplit\\data\n", "✅ 所有模块导入成功！\n"]}], "source": ["# 初始化设置\n", "%matplotlib inline\n", "import os\n", "import sys\n", "import torch\n", "import copy\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# 项目根目录配置\n", "ROOT_DIR = \"fl-exsplit\"  # 根目录名称\n", "DATA_DIR = os.path.join(os.getcwd(), \"data\")  # 数据路径\n", "\n", "# 自动路径修正\n", "if os.path.basename(os.getcwd()) != ROOT_DIR:\n", "    base_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "    if os.path.basename(base_path) == ROOT_DIR:\n", "        os.chdir(base_path)\n", "    else:\n", "        raise FileNotFoundError(f\"请确保Notebook在{ROOT_DIR}目录下运行\")\n", "\n", "sys.path.insert(0, '.')  # 添加项目根目录到路径\n", "\n", "# 设置中文字体（根据系统选择）\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows\n", "# plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # macOS\n", "# plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']  # Linux\n", "\n", "# 解决负号显示问题\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"数据存储路径: {DATA_DIR}\")\n", "\n", "# 验证模块导入\n", "try:\n", "    from configs import params\n", "    from src.data_loader import prepare_mnist_full\n", "    from src.models import SimpleMLP\n", "    from src.algorithms import FedAvg, FedExSplit\n", "    print(\"✅ 所有模块导入成功！\")\n", "except ImportError as e:\n", "    print(f\"❌ 导入失败: {e}\")\n", "    raise"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch>=2.0.1\n", "torchvision>=0.15.2\n", "matplotlib>=3.9.0\n", "tqdm>=4.66.1\n"]}], "source": ["# 打印所有第三方库的版本\n", "import torch\n", "import torchvision\n", "import matplotlib\n", "import tqdm\n", "\n", "print(\"torch>={}\".format(torch.__version__))\n", "print(\"torchvision>={}\".format(torchvision.__version__))\n", "print(\"matplotlib>={}\".format(matplotlib.__version__))\n", "print(\"tqdm>={}\".format(tqdm.__version__))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 设备配置与数据准备"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总数据规模统计：\n", "- 客户端总数: 10\n", "- 客户端 1: 6000 个样本\n", "- 客户端 2: 6000 个样本\n", "- 客户端 3: 6000 个样本\n", "- 客户端 4: 6000 个样本\n", "- 客户端 5: 6000 个样本\n", "- 客户端 6: 6000 个样本\n", "- 客户端 7: 6000 个样本\n", "- 客户端 8: 6000 个样本\n", "- 客户端 9: 6000 个样本\n", "- 客户端 10: 6000 个样本\n", "- 测试集规模: 10000 个样本\n", "- 训练总样本数: 60000 个样本\n", "- 总数据规模: 70000 个样本\n"]}], "source": ["# 初始化配置\n", "config = params.config\n", "\n", "# 准备数据\n", "client_datasets, test_data = prepare_mnist_full(data_root=\"./data\", config=config)\n", "\n", "# 打印数据规模信息\n", "print(f\"总数据规模统计：\")\n", "print(f\"- 客户端总数: {config.num_clients}\")\n", "\n", "# 打印每个客户端数据规模\n", "total_train_samples = 0\n", "for i, dataset in enumerate(client_datasets):\n", "    client_size = len(dataset)\n", "    total_train_samples += client_size\n", "    print(f\"- 客户端 {i+1}: {client_size} 个样本\")\n", "\n", "# 打印测试集规模\n", "test_size = len(test_data)\n", "print(f\"- 测试集规模: {test_size} 个样本\")\n", "print(f\"- 训练总样本数: {total_train_samples} 个样本\")\n", "print(f\"- 总数据规模: {total_train_samples + test_size} 个样本\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 初始化算法"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 创建FedAvg算法实例\n", "fed_avg = FedAvg(config)\n", "\n", "# 初始化服务端和客户端\n", "fed_avg.init_server(test_data)\n", "fed_avg.init_clients(client_datasets)\n", "\n", "# 创建FedExSplit算法实例\n", "fed_exsplit = FedExSplit(config)\n", "\n", "# 初始化服务端和客户端\n", "fed_exsplit.init_server(test_data)\n", "fed_exsplit.init_clients(client_datasets)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 联邦学习训练循环"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始训练FedAvg算法...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ce156671439c4443a42a99e54d9bca5c", "version_major": 2, "version_minor": 0}, "text/plain": ["🦉 联邦学习进度:   0%|          | 0/10 [00:00<?, ?round/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "657e382152ec455a9bfa66feced2602f", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "acb767dd744c458bbd95972d341dfef6", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "af5bbc4b1fbd4576846140bc1d669b44", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "86c19471934b4f8fb6dc96ca8228ef1e", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "739f6c71ce9b429eac0612eb84e2ad62", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c477eacd93a542a98745e405a3b7d6d5", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "229ed047c9f44445b171c099d8f9a4eb", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "349ce062357743eb8721b9d1e3780b1b", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fa70412f6f3c4b948c914022a9b5b808", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "557dcf9c0315494f8e1056fefdc4f85d", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["开始训练FedExSplit算法...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dbda1a20d3844e25a9ae6a9811c665ee", "version_major": 2, "version_minor": 0}, "text/plain": ["🦉 联邦学习进度:   0%|          | 0/10 [00:00<?, ?round/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "77f1ecc744b14d08a7488a3f4ea7c103", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "69d6cccd25d34372aafd175a72a4a90b", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "44946a586c3a4fc79e09af9a60681b7f", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "44322a69458a4bac83c01863e86ac137", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5fb419aca565405eaa2e3e6657bbb682", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d5968912aed847e4bb96d3d0e6320844", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "978cdc12c7e34973922d61e4cbd5b7a5", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d0b52912c46d4413a3fb052a3ca933ce", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "471f637c18bd4b1296659143db073edd", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2c6fbabeebfa4bdcb55afb7b86e26725", "version_major": 2, "version_minor": 0}, "text/plain": ["🚀 客户端更新:   0%|          | 0/10 [00:00<?, ?client/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 保存模型\n", "# 创建保存目录\n", "import os\n", "save_dir = \"saved_models\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "# 保存FedAvg模型\n", "torch.save({\n", "    'model_state_dict': fed_avg.server.global_model.state_dict(),\n", "    'accuracy_history': fed_avg_accuracy_history,\n", "    'config': config\n", "}, os.path.join(save_dir, 'fedavg_model.pt'))\n", "print(f\"FedAvg模型已保存到 {os.path.join(save_dir, 'fedavg_model.pt')}\")\n", "\n", "# 保存FedExSplit模型\n", "torch.save({\n", "    'model_state_dict': fed_exsplit.server.global_model.state_dict(),\n", "    'accuracy_history': fed_exsplit_accuracy_history,\n", "    'config': config\n", "}, os.path.join(save_dir, 'fedexsplit_model.pt'))\n", "print(f\"FedExSplit模型已保存到 {os.path.join(save_dir, 'fedexsplit_model.pt')}\")\n", "\n", "# 保存准确率历史记录为CSV文件，方便后续分析\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# 创建DataFrame并保存\n", "results_df = pd.DataFrame({\n", "    'round': np.arange(1, len(fed_avg_accuracy_history) + 1),\n", "    'fedavg_accuracy': fed_avg_accuracy_history,\n", "    'fedexsplit_accuracy': fed_exsplit_accuracy_history\n", "})\n", "results_df.to_csv(os.path.join(save_dir, 'accuracy_history.csv'), index=False)\n", "print(f\"准确率历史记录已保存到 {os.path.join(save_dir, 'accuracy_history.csv')}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 绘图"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化两种算法的准确率对比\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(fed_avg_accuracy_history, 'bo-', linewidth=2, markersize=8, label='FedAvg')\n", "plt.plot(fed_exsplit_accuracy_history, 'ro-', linewidth=2, markersize=8, label='FedExSplit')\n", "plt.xlabel(\"Communication Rounds\", fontsize=12)\n", "plt.ylabel(\"Test Accuracy\", fontsize=12)\n", "plt.title(\"Performance Comparison: FedAvg vs FedExSplit\", fontsize=14, pad=20)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(range(config.comm_rounds))\n", "plt.legend(fontsize=12)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def test_single_image(model, image, label, model_name=\"\"):\n", "    \"\"\"测试单个图像的识别\"\"\"\n", "    model.eval()\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    image = image.to(device)\n", "    \n", "    with torch.no_grad():\n", "        output = model(image)\n", "        probs = torch.softmax(output, dim=1).squeeze()\n", "        pred = probs.argmax().item()\n", "    \n", "    plt.figure(figsize=(6, 3))\n", "    plt.subplot(1, 2, 1)\n", "    plt.imshow(image.cpu().squeeze().numpy().reshape(28, 28), cmap='gray')\n", "    plt.title(f\"真实标签: {label}\")\n", "    plt.axis('off')\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    plt.barh(range(len(probs)), probs.cpu().numpy(), color='blue')\n", "    plt.yticks(range(len(probs)), [str(i) for i in range(len(probs))])\n", "    plt.xlabel(\"Probability\")\n", "    plt.title(f\"{model_name}预测结果: {pred}\")\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_idx = 4112  # 可以修改这个索引\n", "sample_image, sample_label = test_data[sample_idx]\n", "sample_image = sample_image.unsqueeze(0)  # 添加batch维度\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "untrained_model = SimpleMLP(config.input_size, config.output_size).to(device)\n", "test_single_image(untrained_model, sample_image, sample_label, \"未训练模型\")\n", "test_single_image(fed_exsplit.server.global_model, sample_image, sample_label, \"训练后模型\")"]}], "metadata": {"kernelspec": {"display_name": "torch-gpu", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}