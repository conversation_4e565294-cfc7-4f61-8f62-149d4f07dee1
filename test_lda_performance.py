#!/usr/bin/env python3
"""
Performance test for FedCIFAR100_LDA optimizations.
Tests the speed of getting dataloaders for all clients.
"""

import sys
import time
import psutil
import os
sys.path.append('/home/<USER>/federated-learning/fl-sim')

from fl_sim.data_processing.fed_cifar import FedCIFAR100_LDA

def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_dataloader_performance(num_clients_list=[50, 100, 200, 500]):
    """Test dataloader creation performance for different client counts."""
    
    print("=== FedCIFAR100_LDA Performance Test ===\n")
    print("Testing optimized data partitioning with memory caching...")
    
    results = {}
    
    for num_clients in num_clients_list:
        print(f"\n{'='*60}")
        print(f"Testing with {num_clients} clients")
        print(f"{'='*60}")
        
        # Record initial memory
        initial_memory = get_memory_usage()
        print(f"Initial memory usage: {initial_memory:.1f} MB")
        
        # Initialize dataset
        start_time = time.time()
        dataset = FedCIFAR100_LDA(
            lda_alpha=0.1,
            num_clients=num_clients,
            transform="none",
            seed=42
        )
        init_time = time.time() - start_time
        
        after_init_memory = get_memory_usage()
        print(f"Memory after initialization: {after_init_memory:.1f} MB")
        print(f"Memory increase during init: {after_init_memory - initial_memory:.1f} MB")
        print(f"Dataset initialization time: {init_time:.2f} seconds")
        
        # Test getting dataloaders for all clients
        print(f"\nGetting dataloaders for all {num_clients} clients...")
        start_time = time.time()
        
        dataloaders = []
        for client_idx in range(num_clients):
            if client_idx % max(1, num_clients // 10) == 0:
                print(f"  Progress: {client_idx}/{num_clients} ({100*client_idx/num_clients:.1f}%)")
            
            train_dl, test_dl = dataset.get_dataloader(
                train_bs=32, 
                test_bs=32, 
                client_idx=client_idx
            )
            dataloaders.append((train_dl, test_dl))
        
        dataloader_time = time.time() - start_time
        final_memory = get_memory_usage()
        
        # Calculate statistics
        train_samples = [len(dl[0].dataset) for dl in dataloaders]
        test_samples = [len(dl[1].dataset) for dl in dataloaders]
        
        results[num_clients] = {
            'init_time': init_time,
            'dataloader_time': dataloader_time,
            'total_time': init_time + dataloader_time,
            'memory_peak': final_memory,
            'memory_increase': final_memory - initial_memory,
            'avg_train_samples': sum(train_samples) / len(train_samples),
            'avg_test_samples': sum(test_samples) / len(test_samples),
            'total_train_samples': sum(train_samples),
            'total_test_samples': sum(test_samples)
        }
        
        print(f"\n📊 Results for {num_clients} clients:")
        print(f"  ⏱️  Dataloader creation time: {dataloader_time:.2f} seconds")
        print(f"  ⏱️  Total time (init + dataloaders): {init_time + dataloader_time:.2f} seconds")
        print(f"  💾 Peak memory usage: {final_memory:.1f} MB")
        print(f"  📈 Memory increase: {final_memory - initial_memory:.1f} MB")
        print(f"  📊 Average train samples per client: {results[num_clients]['avg_train_samples']:.1f}")
        print(f"  📊 Average test samples per client: {results[num_clients]['avg_test_samples']:.1f}")
        
        # Check if we meet the 2-minute target for 500 clients
        if num_clients == 500:
            if dataloader_time <= 120:  # 2 minutes
                print(f"  ✅ PASSED: Dataloader creation time ({dataloader_time:.2f}s) is under 2 minutes!")
                print(f"  🎯 Method 1 (memory caching) is sufficient.")
            else:
                print(f"  ❌ FAILED: Dataloader creation time ({dataloader_time:.2f}s) exceeds 2 minutes.")
                print(f"  🔄 Need to implement Method 2 (disk caching).")
        
        # Clean up
        del dataset
        del dataloaders
        
        # Force garbage collection
        import gc
        gc.collect()
        
        print(f"  🧹 Cleanup completed")
    
    # Summary
    print(f"\n{'='*80}")
    print(f"PERFORMANCE SUMMARY")
    print(f"{'='*80}")
    
    print(f"{'Clients':<10} {'Init(s)':<10} {'DataLoader(s)':<15} {'Total(s)':<10} {'Memory(MB)':<12} {'Status':<15}")
    print(f"{'-'*80}")
    
    for num_clients, result in results.items():
        status = "✅ FAST" if result['dataloader_time'] <= 120 else "❌ SLOW"
        if num_clients < 500:
            status = "📊 TEST"
        
        print(f"{num_clients:<10} {result['init_time']:<10.2f} {result['dataloader_time']:<15.2f} "
              f"{result['total_time']:<10.2f} {result['memory_peak']:<12.1f} {status:<15}")
    
    # Recommendation
    print(f"\n🎯 RECOMMENDATION:")
    if 500 in results and results[500]['dataloader_time'] <= 120:
        print(f"✅ Method 1 (memory caching) is sufficient!")
        print(f"   - 500 clients dataloader creation: {results[500]['dataloader_time']:.2f} seconds")
        print(f"   - Memory usage is acceptable: {results[500]['memory_peak']:.1f} MB")
        print(f"   - No need to implement Method 2 (disk caching)")
    else:
        print(f"❌ Method 1 (memory caching) is too slow for 500 clients")
        print(f"🔄 Recommend implementing Method 2 (disk caching with .h5 files)")
    
    return results

if __name__ == "__main__":
    # Test with increasing client counts
    test_clients = [50, 100, 200]
    
    # Add 500 clients test if system has enough memory
    available_memory_gb = psutil.virtual_memory().available / (1024**3)
    if available_memory_gb >= 4:  # At least 4GB available
        test_clients.append(500)
        print(f"System has {available_memory_gb:.1f} GB available memory. Testing up to 500 clients.")
    else:
        print(f"System has only {available_memory_gb:.1f} GB available memory. Limiting test to 200 clients.")
    
    results = test_dataloader_performance(test_clients)
    
    print(f"\n🏁 Performance test completed!")
