# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Formatting with black & flake8

on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    # Don't run on forked repos.
    if: github.repository_owner == 'wenh06'

    runs-on: ubuntu-20.04
    strategy:
      matrix:
        python-version: ["3.6", "3.7", "3.8", "3.9", "3.10"]

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install --upgrade wheel
        python -m pip install black==22.3.0 flake8 # Testing packages
        python -m pip install -r requirements.txt
    - name: Check code format with black and flake8
      run: |
        black . --check --extend-exclude .ipynb -v --exclude="/build|dist/"
        flake8 . --count --ignore="E501 W503 E203 F841 E402" --show-source --statistics --exclude=./.*,build,dist
