# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Test Algorithm FedPD with PyTest

on:
  push:
    branches: [ test-alg ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    # Don't run on forked repos.
    if: github.repository_owner == 'wenh06'

    runs-on: ubuntu-20.04
    strategy:
      matrix:
        python-version: ["3.8"]

    steps:
    - uses: actions/checkout@v3
    - name: Clear unnecessary system components
      run: |
        echo "Free space:"
        df -h
        sudo rm -rf /usr/local/lib/android # will release about 10 GB if you don't need Android
        sudo rm -rf /usr/share/dotnet # will release about 20GB if you don't need .NET
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
    - name: Install system libraries
      run: |
        sudo apt update
        sudo apt install build-essential ffmpeg libsm6 libxext6 libsndfile1 -y
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        python -m pip install -r requirements.txt
        python -m pip install pytest pytest-cov # Testing packages
    - name: Run test with pytest
      run: |
        pytest -v test/test-algorithms/test_fedpd.py
