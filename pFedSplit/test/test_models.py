"""
"""

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parents[1].resolve()))

import torch

from models import (  # noqa: F401
    MLP,
    FedPDMLP,
    CNNMnist,
    CNNFEMnist,
    CNNFEMnist_Tiny,
    CNNCifar,
    RNN_OriginalFedAvg,
    RNN_StackOverFlow,
    RNN_Sent140,
    ResNet18,
    ResNet10,
    LogisticRegression,
    SVC,
)  # noqa: F401


def test_models():
    """ """
    model = CNNFEMnist_Tiny()
    inp = torch.rand(2, 1, 28, 28)
    out = model(inp)
    assert out.shape == (2, 62)
    # TODO: add more tests
