{"cells": [{"cell_type": "markdown", "id": "2b4e56d6", "metadata": {}, "source": ["[![Open In Studio Lab](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/wenh06/pFedSplit/blob/master/code/fedprox-and-fedsplit.ipynb)\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/wenh06/pFedSplit/blob/master/code/fedprox-and-fedsplit.ipynb)\n", "\n", "# A simplest federated learning problem\n", "\n", "Fit$y = kx$, where $k$ is the variable to be fitted，using mean squared error (MSE) as objective.\n", "\n", "Let device 1 have data points $(0, 2), (1, 2)$,\n", "\n", "and let device2 have data points $(2, 0), (2, 1)$.\n", "\n", "Then solely fitted on device 1, one has solution $k = 2$, while on device2, the result is $k = \\frac{1}{4}$. When the data are gathered to do the fitting, the the result would be $k = \\frac{4}{9}$， which is **NOT** the average from the two devices. One reason is that $k$ has degree 2 in the objective function, rather than linear. Another is that data distribution is heterogeneous across devices."]}, {"cell_type": "code", "execution_count": null, "id": "eb5c834e", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "# ^^^ pyforest auto-imports - don't write above this line\n"]}, {"cell_type": "markdown", "id": "cb8139cd", "metadata": {}, "source": ["## First do some symbolic computation"]}, {"cell_type": "code", "execution_count": null, "id": "a66beac1", "metadata": {}, "outputs": [], "source": ["import sympy as sp\n", "import pytest"]}, {"cell_type": "code", "execution_count": null, "id": "8a334bc1", "metadata": {}, "outputs": [], "source": ["k,z,s = sp.symbols(\"k,z,s\")"]}, {"cell_type": "code", "execution_count": null, "id": "f1aaae44", "metadata": {}, "outputs": [], "source": ["func1 = 4 + (k-2)**2\n", "func2 = 4 * k**2 + (2*k-1)**2\n", "func = func1 + func2\n", "\n", "grad_f1_ns = sp.diff(func1, k)**2\n", "grad_f2_ns = sp.diff(func2, k)**2\n", "grad_f_ns = sp.diff(func, k)**2"]}, {"cell_type": "code", "execution_count": null, "id": "6cd6a56d", "metadata": {}, "outputs": [], "source": ["display(grad_f1_ns), display(grad_f2_ns), display(grad_f_ns);"]}, {"cell_type": "code", "execution_count": null, "id": "860a18e6", "metadata": {}, "outputs": [], "source": ["B_square = sp.simplify((grad_f1_ns + grad_f2_ns) / 2 / grad_f_ns)\n", "B_square"]}, {"cell_type": "code", "execution_count": null, "id": "de279109", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7da48eed", "metadata": {}, "source": ["bounded dissimilarity:\n", "for some $\\epsilon > 0$, $\\exists B_{\\epsilon}$ s.t. $\\forall w \\in \\{ w ~|~ \\lVert \\nabla f(w) > \\epsilon \\rVert \\}$, $B(w) \\leqslant B$."]}, {"cell_type": "code", "execution_count": null, "id": "947045d5", "metadata": {}, "outputs": [], "source": ["sp.plot(B_square, ylim=(0, 2))"]}, {"cell_type": "code", "execution_count": null, "id": "a050b577", "metadata": {}, "outputs": [], "source": ["sp.diff(sp.diff(func1, k), k), sp.diff(sp.diff(func2, k), k)"]}, {"cell_type": "code", "execution_count": null, "id": "095aee43", "metadata": {}, "outputs": [], "source": ["prox_sf1 = func1 + (k-z)**2 / (2*s)\n", "sp.simplify(prox_sf1)"]}, {"cell_type": "code", "execution_count": null, "id": "375022ee", "metadata": {}, "outputs": [], "source": ["prox_sf2 = func2 + (k-z)**2 / (2*s)\n", "prox_sf2"]}, {"cell_type": "code", "execution_count": null, "id": "76a6bf8b", "metadata": {}, "outputs": [], "source": ["m_sf1 = sp.simplify(prox_sf1.subs(k, (4*s+z) / (2*s+1)))\n", "m_sf1"]}, {"cell_type": "code", "execution_count": null, "id": "8e253827", "metadata": {}, "outputs": [], "source": ["m_sf2 = sp.simplify(prox_sf2.subs(k, (4*s+z) / (16*s+1)))\n", "m_sf2"]}, {"cell_type": "code", "execution_count": null, "id": "dbee3487", "metadata": {}, "outputs": [], "source": ["theo_k = 4 * (9 * s + 1) / (32 * s + 9)\n", "theo_k"]}, {"cell_type": "code", "execution_count": null, "id": "0378d174", "metadata": {}, "outputs": [], "source": ["sp.simplify(sp.diff(m_sf1, z).subs(z, theo_k) + sp.diff(m_sf2, z).subs(z, theo_k))"]}, {"cell_type": "code", "execution_count": null, "id": "6b88146c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c578cbb1", "metadata": {}, "source": ["Observations on the Theorem 4 in the FedProx paper:\n", "\n", "1. To simplify, let's take $\\gamma = 0$. In this example, $B$ could not be too small, as observed in the plot of `B_squared`，$L_-$ can be simplified to be approximate to 0, $L\\geqslant 16$. Hence only by setting $\\mu$ large enough，i.e. $s$ small enough here in ($s = \\dfrac{1}{\\mu}$), then $\\rho$ would then be greater than 0 in the theorem.\n", "\n", "2. In neighborhoods of the zeros of $\\lVert \\nabla f \\rVert$, if the zeros are not cancelled by (zeros of ) $\\mathbb{E}_k[\\lVert \\nabla F_k \\rVert]$, then $B$ would tend to infinity rapidly. As a result, in such neighborhoods of zeros of $\\lVert \\nabla f \\rVert$, the assumption of $\\rho > 0$ is no longer true, hence the inequality in the theorem is meaningless. When the data distribution are identical across devices (the ideal case), $B$ would be identically 1, then one would not have the above problem. This is the point mentioned in the FedSplit paper."]}, {"cell_type": "code", "execution_count": null, "id": "aa7d2645", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "484ea08c", "metadata": {}, "source": ["## Numeric computation"]}, {"cell_type": "code", "execution_count": null, "id": "06c0fef4", "metadata": {}, "outputs": [], "source": ["def f1(k):\n", "    \"\"\"loss 1\"\"\"\n", "    return (k - 2)**2 + 4\n", "\n", "def f2(k):\n", "    \"\"\"loss 2\"\"\"\n", "    return 8 * k**2 - 4 * k + 1\n", "\n", "def prox1(z, s):\n", "    if np.isinf(s):\n", "        return 2\n", "    return (4 * s + z) / (2 * s + 1)\n", "\n", "def prox2(z, s):\n", "    if np.isinf(s):\n", "        return 0.25\n", "    return (4 * s + z) / (16 * s + 1)"]}, {"cell_type": "code", "execution_count": null, "id": "8cea5278", "metadata": {}, "outputs": [], "source": ["true_k = 4 / 9"]}, {"cell_type": "markdown", "id": "81a98ec4", "metadata": {}, "source": ["## FedProx"]}, {"cell_type": "code", "execution_count": null, "id": "c483ae85", "metadata": {"scrolled": true}, "outputs": [], "source": ["# init values\n", "k_bar = 0\n", "s = 1e-4\n", "\n", "# theoretical convergence value of k, which is related to s\n", "theo_k = 4 * (9 * s + 1) / (32 * s + 9)\n", "\n", "delta = np.inf\n", "n_iter = 0\n", "\n", "k1, k2 = k_bar, k_bar\n", "loss1 = (k1-2)**2 + (k1-k_bar)**2 / (2*s)  # loss from 1\n", "loss2 = (2*k2 - 1)**2 + (2*k2)**2 + (k2-k_bar)**2 / (2*s)  # loss from 2\n", "loss = loss1 + loss2\n", "\n", "print(f\"init k_bar = {k_bar}, loss = {loss:.8f}\")\n", "\n", "while n_iter < 1e5 and delta > 1e-9:\n", "    k1 = prox1(k_bar, s)\n", "    k2 = prox2(k_bar, s)\n", "    loss1 = (k1-2)**2 + (k1-k_bar)**2 / (2*s)  # loss from 1\n", "    loss2 = (2*k2 - 1)**2 + (2*k2)**2 + (k2-k_bar)**2 / (2*s)  # loss from 2\n", "    loss_decrease = loss - (loss1 + loss2)\n", "    loss = loss1 + loss2\n", "    new_k_bar = 0.5 * (k1 + k2)\n", "    delta = abs(k_bar - new_k_bar)\n", "    k_bar = new_k_bar\n", "    n_iter += 1\n", "    print(f\"n_iter = {n_iter}, k_bar = {k_bar:.8f}, loss = {loss:.12f}, loss decrease = {loss_decrease:.12f}\")\n", "    print(f\"n_iter = {n_iter}, k1 = {k1:.8f}, loss1 = {loss1:.8f}, k2 = {k2}, loss2 = {loss2:.8f}\")\n", "    print(f\"n_iter = {n_iter}, grad_f_ns = {grad_f_ns.subs(k, k_bar)}\")"]}, {"cell_type": "markdown", "id": "01354d6e", "metadata": {}, "source": ["One can find that the total loss is decreasing, and converges to `theo_k`, which however is not the **TRUE** solution to the total problem"]}, {"cell_type": "code", "execution_count": null, "id": "c83429f9", "metadata": {}, "outputs": [], "source": ["k_bar, theo_k, true_k"]}, {"cell_type": "code", "execution_count": null, "id": "ff6c6497", "metadata": {}, "outputs": [], "source": ["k_bar == pytest.approx(theo_k, abs=1e-5)"]}, {"cell_type": "code", "execution_count": null, "id": "45e05494", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "94ca3d6b", "metadata": {}, "source": ["## FedSplit"]}, {"cell_type": "code", "execution_count": null, "id": "599341ac", "metadata": {}, "outputs": [], "source": ["# init values\n", "k_bar = 0\n", "s = 1e-2\n", "\n", "z1, z2 = k_bar, k_bar\n", "delta = np.inf\n", "n_iter = 0\n", "\n", "while n_iter < 1e5 and delta > 1e-10:\n", "    tmp1 = prox1(2 * k_bar - z1, s)\n", "    tmp2 = prox2(2 * k_bar - z2, s)\n", "    z1 = z1 + 2 * (tmp1 - k_bar)\n", "    z2 = z2 + 2 * (tmp2 - k_bar)\n", "    new_k_bar = 0.5 * (z1 + z2)\n", "    delta = abs(k_bar - new_k_bar)\n", "    k_bar = new_k_bar\n", "    n_iter += 1\n", "    print(f\"n_iter = {n_iter}, k_bar = {k_bar:.16f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f8be5d33", "metadata": {}, "outputs": [], "source": ["k_bar, true_k"]}, {"cell_type": "code", "execution_count": null, "id": "97ab76f8", "metadata": {}, "outputs": [], "source": ["k_bar == pytest.approx(true_k, abs=1e-5)"]}, {"cell_type": "code", "execution_count": null, "id": "4363541a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 5}