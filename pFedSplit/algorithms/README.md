# Algorithms

this folder contains algorithms for federated learning

1. [FedProx](https://github.com/litian96/FedProx) ![test-fedprox](https://github.com/wenh06/pFedSplit/actions/workflows/test-fedprox.yml/badge.svg)
2. [FedOpt](https://arxiv.org/abs/2003.00295) ![test-fedopt](https://github.com/wenh06/pFedSplit/actions/workflows/test-fedopt.yml/badge.svg)
3. [pFedMe](https://github.com/CharlieDinh/pFedMe) ![test-pfedme](https://github.com/wenh06/pFedSplit/actions/workflows/test-pfedme.yml/badge.svg)
4. [FedSplit](https://arxiv.org/abs/2005.05238) ![test-fedsplit](https://github.com/wenh06/pFedSplit/actions/workflows/test-fedsplit.yml/badge.svg)
5. [FedDR](https://github.com/unc-optimization/FedDR) ![test-feddr](https://github.com/wenh06/pFedSplit/actions/workflows/test-feddr.yml/badge.svg)
6. [FedPD](https://github.com/564612540/FedPD/) ![test-fedpd](https://github.com/wenh06/pFedSplit/actions/workflows/test-fedpd.yml/badge.svg)
7. [SCAFFOLD](https://proceedings.mlr.press/v119/karimireddy20a.html) ![test-scaffold](https://github.com/wenh06/pFedSplit/actions/workflows/test-scaffold.yml/badge.svg)
8. [ProxSkip](https://proceedings.mlr.press/v162/mishchenko22b.html) ![test-proxskip](https://github.com/wenh06/pFedSplit/actions/workflows/test-proxskip.yml/badge.svg)
9. [Ditto](https://arxiv.org/abs/2012.04221) ![test-ditto](https://github.com/wenh06/pFedSplit/actions/workflows/test-ditto.yml/badge.svg)
