# FROM pytorch/pytorch:1.8.0-cuda11.1-cudnn8-devel
# FROM pytorch/pytorch:1.10.0-cuda11.3-cudnn8-runtime
FROM python/python:3.8-slim

## The MAINTAINER instruction sets the author field of the generated images.
LABEL maintainer="<EMAIL>"

RUN mkdir /pFedSplit
COPY ./ /pFedSplit
WORKDIR /pFedSplit


## Install your dependencies here using apt install, etc.

RUN apt update && apt upgrade -y && apt clean
RUN apt install ffmpeg libsm6 libxext6 tar unzip wget vim nano -y

# RUN apt install python3-pip
RUN ln -s /usr/bin/python3 /usr/bin/python && ln -s /usr/bin/pip3 /usr/bin/pip
# RUN pip install --upgrade pip

# http://mirrors.aliyun.com/pypi/simple/
# http://pypi.douban.com/simple/
# RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
## Include the following line if you have a requirements.txt file.
RUN pip install -r requirements-no-torch.txt
RUN pip install torch==1.10.0+cu113 -f https://download.pytorch.org/whl/torch_stable.html
RUN pip install torchvision==0.11.1+cu113 --no-deps -f https://download.pytorch.org/whl/torch_stable.html
RUN pip install torch-optimizer --no-deps
RUN python -m pip cache purge

RUN python docker_test.py
