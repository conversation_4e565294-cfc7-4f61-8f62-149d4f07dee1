"""
The dataset Sent140 used in the FedProx paper

References
----------
1. https://github.com/litian96/FedProx/tree/master/data/sent140
"""

import json  # noqa: F401
from pathlib import Path
from typing import Optional, Union, List, Tuple, Dict

import numpy as np  # noqa: F401
import torch  # noqa: F401
import torch.utils.data as data

from misc import CACHED_DATA_DIR
from models import nn as mnn  # noqa: F401
from models.utils import top_n_accuracy  # noqa: F401
from .fed_dataset import FedNLPDataset  # noqa: F401


__all__ = [
    "LeafSent140",
]


LEAF_SENT140_DATA_DIR = CACHED_DATA_DIR / "leaf_sent140"
LEAF_SENT140_DATA_DIR.mkdir(parents=True, exist_ok=True)


class LeafSent140(FedNLPDataset):
    """ """

    __name__ = "LeafSent140"

    def _preload(self, datadir: Optional[Union[str, Path]] = None) -> None:
        """ """
        raise NotImplementedError

    def get_dataloader(
        self,
        train_bs: Optional[int] = None,
        test_bs: Optional[int] = None,
        client_idx: Optional[int] = None,
    ) -> Tuple[data.DataLoader, data.DataLoader]:
        """ """
        raise NotImplementedError

    @property
    def url(self) -> str:
        return "http://cs.stanford.edu/people/alecmgo/trainingandtestdata.zip"

    @property
    def candidate_models(self) -> Dict[str, torch.nn.Module]:
        """
        a set of candidate models
        """
        return {
            "rnn": mnn.RNN_Sent140(),
        }

    @property
    def doi(self) -> List[str]:
        return ["10.48550/ARXIV.1812.01097"]
