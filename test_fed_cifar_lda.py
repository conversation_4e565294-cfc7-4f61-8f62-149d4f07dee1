#!/usr/bin/env python3
"""
Test script for FedCIFAR100_LDA modifications.
This script tests the test dataset allocation logic.
"""

import sys
import os
sys.path.append('/home/<USER>/federated-learning/fl-sim')

from fl_sim.data_processing.fed_cifar import FedCIFAR100_LDA

def test_fed_cifar100_lda():
    """Test FedCIFAR100_LDA with different client numbers."""
    
    print("=== Testing FedCIFAR100_LDA Test Dataset Allocation ===\n")
    
    # Test case 1: num_clients < 100 (original test clients)
    print("Test Case 1: num_clients = 50 (< 100)")
    try:
        dataset1 = FedCIFAR100_LDA(
            lda_alpha=0.1,
            num_clients=50,
            transform="none",
            seed=42
        )
        
        print(f"  - Number of clients: {dataset1.num_clients}")
        print(f"  - LDA alpha: {dataset1.lda_alpha}")
        print(f"  - Test partition map keys: {len(dataset1.test_partition_map)}")
        
        # Check a few clients' test allocations
        for i in [0, 1, 49]:
            test_clients = dataset1.test_partition_map[i]
            print(f"  - Client {i} gets test data from original clients: {test_clients}")
        
        # Test getting dataloader for client 0
        train_dl, test_dl = dataset1.get_dataloader(train_bs=32, test_bs=32, client_idx=0)
        print(f"  - Client 0 train samples: {len(train_dl.dataset)}")
        print(f"  - Client 0 test samples: {len(test_dl.dataset)}")
        
        print("  ✓ Test Case 1 passed\n")
        
    except Exception as e:
        print(f"  ✗ Test Case 1 failed: {e}\n")
        return False
    
    # Test case 2: num_clients > 100 (original test clients)
    print("Test Case 2: num_clients = 150 (> 100)")
    try:
        dataset2 = FedCIFAR100_LDA(
            lda_alpha=0.1,
            num_clients=150,
            transform="none",
            seed=42
        )
        
        print(f"  - Number of clients: {dataset2.num_clients}")
        print(f"  - Test partition map keys: {len(dataset2.test_partition_map)}")
        
        # Check cyclic assignment
        for i in [0, 50, 100, 149]:
            test_clients = dataset2.test_partition_map[i]
            expected_client = i % 100
            print(f"  - Client {i} gets test data from original client: {test_clients[0]} (expected: {expected_client})")
            assert test_clients[0] == expected_client, f"Expected {expected_client}, got {test_clients[0]}"
        
        # Test getting dataloader for client 100
        train_dl, test_dl = dataset2.get_dataloader(train_bs=32, test_bs=32, client_idx=100)
        print(f"  - Client 100 train samples: {len(train_dl.dataset)}")
        print(f"  - Client 100 test samples: {len(test_dl.dataset)}")
        
        print("  ✓ Test Case 2 passed\n")
        
    except Exception as e:
        print(f"  ✗ Test Case 2 failed: {e}\n")
        return False
    
    # Test case 3: num_clients = 100 (equal to original test clients)
    print("Test Case 3: num_clients = 100 (= 100)")
    try:
        dataset3 = FedCIFAR100_LDA(
            lda_alpha=0.1,
            num_clients=100,
            transform="none",
            seed=42
        )
        
        print(f"  - Number of clients: {dataset3.num_clients}")
        print(f"  - Test partition map keys: {len(dataset3.test_partition_map)}")
        
        # Each client should get exactly one original test client
        for i in [0, 50, 99]:
            test_clients = dataset3.test_partition_map[i]
            print(f"  - Client {i} gets test data from original clients: {test_clients}")
            assert len(test_clients) == 1, f"Expected 1 test client, got {len(test_clients)}"
            assert test_clients[0] == i, f"Expected {i}, got {test_clients[0]}"
        
        print("  ✓ Test Case 3 passed\n")
        
    except Exception as e:
        print(f"  ✗ Test Case 3 failed: {e}\n")
        return False
    
    print("=== All tests passed! ===")
    return True

if __name__ == "__main__":
    success = test_fed_cifar100_lda()
    sys.exit(0 if success else 1)
