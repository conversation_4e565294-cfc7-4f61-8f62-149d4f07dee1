import torch
import torch.nn as nn
import torch.optim as optim
import torch.multiprocessing as mp
import time
import argparse
import os
import copy # Needed for deep copying model state if we were sending it

# --- Simple Model Definition ---
class SimpleMLP(nn.Module):
    # (Same as before)
    def __init__(self, input_dim, hidden_dim=64, output_dim=10):
        super().__init__()
        self.layer1 = nn.Linear(input_dim, hidden_dim)
        self.relu = nn.ReLU()
        self.layer2 = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        x = self.layer1(x.view(x.size(0), -1))
        x = self.relu(x)
        x = self.layer2(x)
        return x

# --- Simulate Client Training Loop (Core Compute) ---
def simulate_client_training_compute(model, optimizer, criterion, device,
                                     local_epochs, batches_per_epoch, batch_size, input_dim, output_dim,
                                     stream=None):
    context = torch.cuda.stream(stream) if stream else torch.cuda.stream(torch.cuda.default_stream(device))
    with context:
        model.train()
        for epoch in range(local_epochs):
            for batch in range(batches_per_epoch):
                x_cpu = torch.randn(batch_size, input_dim, dtype=torch.float32).pin_memory()
                y_cpu = torch.randint(0, output_dim, (batch_size,), dtype=torch.long).pin_memory()
                x_gpu = x_cpu.to(device, non_blocking=bool(stream))
                y_gpu = y_cpu.to(device, non_blocking=bool(stream))
                optimizer.zero_grad(set_to_none=True)
                outputs = model(x_gpu)
                loss = criterion(outputs, y_gpu)
                loss.backward()
                optimizer.step()

# --- Worker Process Resource Cache ---
# This dictionary is local to each worker process when using 'spawn'
_worker_resource_cache = {}

# --- Worker Function Helper: Initialize or Get Resources ---
def get_or_init_worker_resources(worker_id, client_indices, device_str, model_params):
    """
    Ensures models and optimizers for the specified clients exist for this worker.
    Uses a process-local cache (_worker_resource_cache).
    This function is called *before* the timed computation within the worker.
    """
    global _worker_resource_cache
    device = torch.device(device_str)
    torch.cuda.set_device(device) # Ensure device is set for potential initialization

    initialized_count = 0
    resource_dict = {} # Collect resources for this specific call

    for client_idx in client_indices:
        cache_key = client_idx # Use client index as the key for simplicity
        if cache_key not in _worker_resource_cache:
            # Initialize if not in this process's cache
            print(f"  Worker {os.getpid()} initializing model for client {client_idx} on {device}") # Debug Init
            model = SimpleMLP(model_params['input_dim'], model_params['hidden_dim'], model_params['output_dim']).to(device)
            optimizer = optim.SGD(model.parameters(), lr=0.01)
            _worker_resource_cache[cache_key] = (model, optimizer)
            initialized_count += 1
        resource_dict[client_idx] = _worker_resource_cache[cache_key]

    # if initialized_count > 0: # Debug Init
    #     print(f"  Worker {os.getpid()} initialized {initialized_count} new resources.")

    return resource_dict # Return resources needed for *this* call

# --- Scenario 1: Sequential Execution (Pre-initialized Models) ---
def run_sequential_training_preinit(num_clients, target_device, model_params, train_params):
    device = torch.device(target_device)
    print(f"--- 场景 1: 顺序执行 (单进程, 默认流, on {device}, 预初始化模型) ---")
    torch.cuda.set_device(device)

    print("  预初始化模型 (场景 1)...")
    criterion = nn.CrossEntropyLoss()
    client_resources = {}
    for client_idx in range(num_clients):
        model = SimpleMLP(model_params['input_dim'], model_params['hidden_dim'], model_params['output_dim']).to(device)
        optimizer = optim.SGD(model.parameters(), lr=0.01)
        client_resources[client_idx] = (model, optimizer)
    print("  模型预初始化完成.")

    torch.cuda.synchronize(device=device)
    total_start_time = time.perf_counter()

    for client_idx in range(num_clients):
        model, optimizer = client_resources[client_idx]
        simulate_client_training_compute(
            model, optimizer, criterion, device,
            train_params['local_epochs'], train_params['batches_per_epoch'],
            train_params['batch_size'], model_params['input_dim'], model_params['output_dim'],
            stream=None
        )

    torch.cuda.synchronize(device=device)
    total_end_time = time.perf_counter()
    total_time = total_end_time - total_start_time
    print(f"总计算时间: {total_time:.6f} 秒")
    return total_time

# --- Scenario 2: Multiprocessing (Default Stream, Worker Pre-Init) ---
def worker_task_default_stream(worker_id, client_indices, assigned_device_str, model_params, train_params):
    """Worker function called by starmap for each benchmark run."""
    # 1. Get pre-initialized resources (initializes on first call per process)
    # This setup time happens *before* the main starmap timer starts measuring compute
    # BUT, for fairness, let's keep the resource getting inside the starmap scope
    # as it might involve some lookup cost. The actual model creation should only happen once though.
    worker_resources = get_or_init_worker_resources(worker_id, client_indices, assigned_device_str, model_params)

    # --- This is the part timed by the main process's starmap timer ---
    device = torch.device(assigned_device_str)
    criterion = nn.CrossEntropyLoss()

    for client_idx in client_indices:
        model, optimizer = worker_resources[client_idx] # Fast lookup
        simulate_client_training_compute(
            model, optimizer, criterion, device,
            train_params['local_epochs'], train_params['batches_per_epoch'],
            train_params['batch_size'], model_params['input_dim'], model_params['output_dim'],
            stream=None
        )

    torch.cuda.synchronize(device=device)
    return True

# --- Scenario 3: Multiprocessing (Multi-Stream, Worker Pre-Init) ---
def worker_task_multi_stream(worker_id, client_indices, assigned_device_str, model_params, train_params, num_streams_per_worker):
    """Worker function called by starmap for each benchmark run."""
    # 1. Get pre-initialized resources
    worker_resources = get_or_init_worker_resources(worker_id, client_indices, assigned_device_str, model_params)

    # --- Timed Computation ---
    device = torch.device(assigned_device_str)
    criterion = nn.CrossEntropyLoss()
    if num_streams_per_worker <= 0: return False
    # Create streams for this specific execution run
    streams = [torch.cuda.Stream(device=device) for _ in range(num_streams_per_worker)]

    for i, client_idx in enumerate(client_indices):
        stream_idx = i % num_streams_per_worker
        current_stream = streams[stream_idx]
        model, optimizer = worker_resources[client_idx] # Fast lookup

        simulate_client_training_compute(
            model, optimizer, criterion, device,
            train_params['local_epochs'], train_params['batches_per_epoch'],
            train_params['batch_size'], model_params['input_dim'], model_params['output_dim'],
            stream=current_stream
        )

    for stream in streams:
        stream.synchronize()
    return True


# --- Main Execution Logic (Persistent Pool, Calls Worker Tasks) ---
def main(args):
    try:
        mp.set_start_method('spawn', force=True)
        print("使用 'spawn' 启动方法.")
    except RuntimeError:
        print("无法设置 'spawn' 启动方法 (可能已设置或不支持).")

    # --- GPU Handling & Parameter Setup ---
    # ... (Same GPU validation, parameter printing, worker adjustment) ...
    gpu_ids = args.gpus
    sequential_device = f"cuda:{gpu_ids[0]}"
    num_workers = args.num_workers
    model_params = { 'input_dim': args.input_dim, 'hidden_dim': args.hidden_dim, 'output_dim': 10 }
    train_params = { 'local_epochs': args.local_epochs, 'batches_per_epoch': args.batches_per_epoch, 'batch_size': args.batch_size, }

    # --- Pre-allocate tasks to workers ---
    # ... (Same worker assignment logic as before to create task arguments) ...
    all_client_indices = list(range(args.num_clients))
    tasks_per_worker = args.num_clients // num_workers
    worker_assignments_default = []
    worker_assignments_multi = []
    start_idx = 0
    num_gpus = len(gpu_ids)
    for i in range(num_workers):
        end_idx = start_idx + tasks_per_worker + (1 if i < args.num_clients % num_workers else 0)
        assigned_indices = all_client_indices[start_idx:end_idx]
        if assigned_indices:
            assigned_gpu_idx = gpu_ids[i % num_gpus]
            assigned_device_str = f"cuda:{assigned_gpu_idx}"
            worker_assignments_default.append((i, assigned_indices, assigned_device_str, model_params, train_params))
            worker_assignments_multi.append((i, assigned_indices, assigned_device_str, model_params, train_params, args.num_streams_per_worker))
        start_idx = end_idx
    if not worker_assignments_default: return

    # === Create Persistent Pool ===
    print(f"\n创建包含 {num_workers} 个 worker 进程的持久 Pool...")
    # NOTE: The initializer function is NO LONGER passed here.
    # Initialization happens lazily inside the worker task function.
    pool = mp.Pool(processes=num_workers)
    print("Pool 已创建. Worker将在首次任务调用时惰性初始化资源。")
    # ==============================

    results = {'seq': [], 'mp_default': [], 'mp_multi': []}

    # --- Run Benchmarks ---
    try:
        # --- Optional: Force Worker Initialization Before Timing (Warmup) ---
        print("\n执行预热运行以确保 worker 资源已初始化...")
        # We run *both* worker types once to ensure all necessary models might be created
        # Ignore the timing here, just ensures caches are populated.
        pool.starmap(worker_task_default_stream, worker_assignments_default)
        pool.starmap(worker_task_multi_stream, worker_assignments_multi)
        print("预热运行完成.")
        # -----------------------------------------------------------------

        for r in range(args.runs + 1): # Use range(1, args.runs + 1) if warmup handled ^
            is_warmup = (r == 0) # Treat first run as warmup if not explicitly done above
            if is_warmup:
                 print("\n--- 第 预热 运行 (计时仅用于参考) ---")
            else:
                 print(f"\n--- 第 {r} 次计时运行 ---")


            # --- Scenario 1 ---
            # Note: Sequential pre-init happens inside its function now
            t_seq = run_sequential_training_preinit(args.num_clients, sequential_device, model_params, train_params)
            if not is_warmup: results['seq'].append(t_seq)


            # --- Scenario 2 ---
            print(f"--- 场景 2: 多进程 ({num_workers} workers, 默认流, GPUs {gpu_ids}, 惰性初始化) ---")
            for gpu_id in gpu_ids: torch.cuda.synchronize(device=f"cuda:{gpu_id}")
            start_time_mp_default = time.perf_counter()
            # Dispatch work - worker handles getting/init resources internally
            pool.starmap(worker_task_default_stream, worker_assignments_default)
            end_time_mp_default = time.perf_counter()
            t_mp_default = end_time_mp_default - start_time_mp_default
            print(f"总计算时间: {t_mp_default:.6f} 秒")
            if not is_warmup: results['mp_default'].append(t_mp_default)


            # --- Scenario 3 ---
            print(f"--- 场景 3: 多进程 ({num_workers} workers, {args.num_streams_per_worker} 流, GPUs {gpu_ids}, 惰性初始化) ---")
            for gpu_id in gpu_ids: torch.cuda.synchronize(device=f"cuda:{gpu_id}")
            start_time_mp_multi = time.perf_counter()
            pool.starmap(worker_task_multi_stream, worker_assignments_multi)
            end_time_mp_multi = time.perf_counter()
            t_mp_multi = end_time_mp_multi - start_time_mp_multi
            print(f"总计算时间: {t_mp_multi:.6f} 秒")
            if not is_warmup: results['mp_multi'].append(t_mp_multi)

            print("-" * 40 )

    finally:
        # === Clean up Persistent Pool ===
        print("\n关闭 Pool...")
        pool.close()
        pool.join()
        print("Pool 已关闭.")
        # ==============================

    # --- Calculate average times and print results ---
    # ... (rest of the result calculation and printing code remains the same) ...
    avg_seq = sum(results['seq']) / args.runs if results['seq'] else float('inf')
    avg_mp_default = sum(results['mp_default']) / args.runs if results['mp_default'] else float('inf')
    avg_mp_multi = sum(results['mp_multi']) / args.runs if results['mp_multi'] else float('inf')

    print("\n--- 平均计算时间结果 ---")
    print(f"场景 1 (顺序, GPU {gpu_ids[0]}):             {avg_seq:.6f} 秒")
    print(f"场景 2 (MP 默认流, GPUs {gpu_ids}):        {avg_mp_default:.6f} 秒")
    print(f"场景 3 (MP {args.num_streams_per_worker}流/Worker, GPUs {gpu_ids}): {avg_mp_multi:.6f} 秒")

    # Conclusion (same logic as before)
    # ... [conclusion printing code remains the same] ...
    print("\n--- 结论 ---")
    times = {'顺序': avg_seq, 'MP 默认流': avg_mp_default, f'MP {args.num_streams_per_worker}流': avg_mp_multi}
    valid_times = {k: v for k, v in times.items() if v != float('inf')}

    if not valid_times:
        print("所有测试均未成功运行。")
    else:
        best_scenario = min(valid_times, key=valid_times.get)
        best_time = valid_times[best_scenario]
        print(f"最佳: {best_scenario} ({best_time:.6f} 秒)")

        if best_scenario != '顺序' and avg_seq != float('inf'):
            print(f"  - 比 顺序 (单GPU) 快 {avg_seq / best_time:.2f} 倍")

        if best_scenario == f'MP {args.num_streams_per_worker}流' and 'MP 默认流' in valid_times and valid_times['MP 默认流'] != float('inf'):
            if best_time < valid_times['MP 默认流']:
                print(f"  - 比 MP 默认流 快 {valid_times['MP 默认流'] / best_time:.2f} 倍")
            elif best_time > valid_times['MP 默认流']:
                 print(f"  - 比 MP 默认流 慢 {best_time / valid_times['MP 默认流']:.2f} 倍")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='比较模拟FL客户端训练的并行策略性能 (预初始化模型+持久Pool)')
    # Arguments remain the same
    parser.add_argument('--gpus', type=int, nargs='+', required=True, help='要使用的GPU ID列表 (例如: 0 或 0 1)')
    parser.add_argument('--num_clients', type=int, default=40, help='模拟的客户端总数')
    parser.add_argument('--local_epochs', type=int, default=2, help='每个客户端本地训练的Epochs数')
    parser.add_argument('--batches_per_epoch', type=int, default=10, help='每个Epoch的Batch数')
    parser.add_argument('--batch_size', type=int, default=64, help='Batch大小')
    parser.add_argument('--input_dim', type=int, default=784, help='模型输入维度')
    parser.add_argument('--hidden_dim', type=int, default=128, help='模型隐藏层维度')
    parser.add_argument('--num_workers', type=int, default=8, help='多进程场景下的Worker数量')
    parser.add_argument('--num_streams_per_worker', type=int, default=1, help='场景3中每个Worker使用的流数量 (强烈建议从1开始)') # Default to 1 stream
    parser.add_argument('--runs', type=int, default=3, help='测试运行次数 (不含预热)')
    args = parser.parse_args()
    main(args)