import torch
import torch.nn as nn
import torch.optim as optim
import torch.multiprocessing as mp
import time
import argparse
import os

# --- Simple Model Definition ---
class SimpleMLP(nn.Module):
    # (Same as before)
    def __init__(self, input_dim, hidden_dim=64, output_dim=10):
        super().__init__()
        self.layer1 = nn.Linear(input_dim, hidden_dim)
        self.relu = nn.ReLU()
        self.layer2 = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        x = self.layer1(x.view(x.size(0), -1)) # Flatten input
        x = self.relu(x)
        x = self.layer2(x)
        return x

# --- Simulate Client Training Loop ---
# This function runs the core computation for one client
# Timing Note: The timer in the calling functions STARTS *before* this function
# is first called within a round/worker.
def simulate_client_training(model, optimizer, criterion, device,
                             local_epochs, batches_per_epoch, batch_size, input_dim, output_dim,
                             stream=None): # stream is optional

    context = torch.cuda.stream(stream) if stream else torch.cuda.stream(torch.cuda.default_stream(device))

    with context:
        model.train() # Set model to training mode
        # Simulate receiving data just before the first batch processing
        for epoch in range(local_epochs):
            for batch in range(batches_per_epoch):
                # --- TIMING FOCUS: Starts effectively here for the client's work ---
                # 1. Simulate data generation and transfer (as if received)
                x_cpu = torch.randn(batch_size, input_dim, dtype=torch.float32).pin_memory()
                y_cpu = torch.randint(0, output_dim, (batch_size,), dtype=torch.long).pin_memory()
                x_gpu = x_cpu.to(device, non_blocking=bool(stream))
                y_gpu = y_cpu.to(device, non_blocking=bool(stream))

                # 2. Standard Training Steps
                optimizer.zero_grad(set_to_none=True) # Slightly more efficient
                outputs = model(x_gpu)
                loss = criterion(outputs, y_gpu)
                loss.backward()
                optimizer.step()
        # --- TIMING FOCUS: Ends effectively here for the client's work ---

# --- Scenario 1: Sequential Execution (on First Specified GPU) ---
def run_sequential_training(num_clients, target_device, model_params, train_params):
    device = torch.device(target_device)
    print(f"--- 场景 1: 顺序执行 (单进程, 默认流, on {device}) ---")
    torch.cuda.set_device(device) # Ensure correct device context

    # Instantiate reusable components if possible, though model/optim often per-client
    criterion = nn.CrossEntropyLoss()

    torch.cuda.synchronize(device=device) # Sync before starting timer
    total_start_time = time.perf_counter()

    for client_idx in range(num_clients):
        # Create model and optimizer INSIDE the timed loop - simulates per-client setup
        model = SimpleMLP(model_params['input_dim'], model_params['hidden_dim'], model_params['output_dim']).to(device)
        optimizer = optim.SGD(model.parameters(), lr=0.01) # LR doesn't matter

        # Simulate training on the default stream
        simulate_client_training(
            model, optimizer, criterion, device,
            train_params['local_epochs'], train_params['batches_per_epoch'],
            train_params['batch_size'], model_params['input_dim'], model_params['output_dim'],
            stream=None # Use default stream
        )

    # Wait for all operations on the default stream of the target device to complete
    torch.cuda.synchronize(device=device)
    total_end_time = time.perf_counter()
    total_time = total_end_time - total_start_time
    print(f"总时间: {total_time:.6f} 秒")
    return total_time

# --- Scenario 2: Multiprocessing (Default Stream per Worker, Distributed GPUs) ---

# Worker function for Scenario 2
def worker_train_default_stream(worker_id, client_indices, assigned_device_str, model_params, train_params):
    # Initialization (Model/Optimizer creation) happens INSIDE the worker,
    # thus included in the main process's timed starmap call.
    device = torch.device(assigned_device_str)
    torch.cuda.set_device(device) # CRITICAL for each process
    # print(f"Worker {worker_id} (Default) started on {device} for clients {client_indices}")

    criterion = nn.CrossEntropyLoss()

    for client_idx in client_indices:
        # Each client simulation gets its own model/optimizer in this worker
        model = SimpleMLP(model_params['input_dim'], model_params['hidden_dim'], model_params['output_dim']).to(device)
        optimizer = optim.SGD(model.parameters(), lr=0.01)

        # Simulate training on this worker's default stream
        simulate_client_training(
            model, optimizer, criterion, device,
            train_params['local_epochs'], train_params['batches_per_epoch'],
            train_params['batch_size'], model_params['input_dim'], model_params['output_dim'],
            stream=None # Use default stream
        )

    # CRITICAL: Ensure all GPU work submitted by THIS worker on ITS device is done
    torch.cuda.synchronize(device=device)
    # print(f"Worker {worker_id} (Default) finished.")
    return True

def run_mp_default_stream_training(num_clients, gpu_ids, model_params, train_params, num_workers):
    num_gpus = len(gpu_ids)
    print(f"--- 场景 2: 多进程 ({num_workers} workers, 每个使用默认流, 分布在 GPUs {gpu_ids}) ---")
    if num_workers <= 0 or num_gpus <= 0: return float('inf')

    # Distribute client indices among workers and assign GPUs round-robin
    all_client_indices = list(range(num_clients))
    tasks_per_worker = num_clients // num_workers
    worker_assignments = []
    start_idx = 0
    for i in range(num_workers):
        end_idx = start_idx + tasks_per_worker + (1 if i < num_clients % num_workers else 0)
        assigned_indices = all_client_indices[start_idx:end_idx]
        if assigned_indices:
            # Assign GPU to worker
            assigned_gpu_idx = gpu_ids[i % num_gpus]
            assigned_device_str = f"cuda:{assigned_gpu_idx}"
            worker_assignments.append((i, assigned_indices, assigned_device_str, model_params, train_params))
        start_idx = end_idx

    if not worker_assignments: return float('inf')

    # Sync all target GPUs before starting the main timer
    for gpu_id in gpu_ids:
         torch.cuda.synchronize(device=f"cuda:{gpu_id}")

    start_time = time.perf_counter() # Timer starts before pool creation/task submission
    with mp.Pool(processes=num_workers) as pool:
        results = pool.starmap(worker_train_default_stream, worker_assignments)
    end_time = time.perf_counter() # Timer ends after all workers finished

    total_time = end_time - start_time
    print(f"总时间: {total_time:.6f} 秒")
    return total_time


# --- Scenario 3: Multiprocessing (Multi-Stream per Worker, Distributed GPUs) ---

# Worker function for Scenario 3
def worker_train_multi_stream(worker_id, client_indices, assigned_device_str, model_params, train_params, num_streams_per_worker):
    # Initialization (Model/Optimizer/Stream creation) happens INSIDE the worker.
    device = torch.device(assigned_device_str)
    torch.cuda.set_device(device) # CRITICAL
    # print(f"Worker {worker_id} (Multi) started on {device} for {len(client_indices)} clients using {num_streams_per_worker} streams")

    if num_streams_per_worker <= 0: return False
    streams = [torch.cuda.Stream(device=device) for _ in range(num_streams_per_worker)]
    criterion = nn.CrossEntropyLoss()

    # Pre-create models/optimizers for clients assigned to this worker
    client_resources = {}
    for client_idx in client_indices:
         model = SimpleMLP(model_params['input_dim'], model_params['hidden_dim'], model_params['output_dim']).to(device)
         optimizer = optim.SGD(model.parameters(), lr=0.01)
         client_resources[client_idx] = (model, optimizer)

    # Assign clients to streams and run simulations
    for i, client_idx in enumerate(client_indices):
        stream_idx = i % num_streams_per_worker
        current_stream = streams[stream_idx]
        model, optimizer = client_resources[client_idx]

        simulate_client_training(
            model, optimizer, criterion, device,
            train_params['local_epochs'], train_params['batches_per_epoch'],
            train_params['batch_size'], model_params['input_dim'], model_params['output_dim'],
            stream=current_stream # Use the specific stream
        )

    # CRITICAL: Wait for all streams used by THIS worker on ITS device to finish
    for stream in streams:
        stream.synchronize()

    # print(f"Worker {worker_id} (Multi) finished.")
    return True


def run_mp_multi_stream_training(num_clients, gpu_ids, model_params, train_params, num_workers, num_streams_per_worker):
    num_gpus = len(gpu_ids)
    print(f"--- 场景 3: 多进程 ({num_workers} workers, 每个使用 {num_streams_per_worker} 个流, 分布在 GPUs {gpu_ids}) ---")
    if num_workers <= 0 or num_streams_per_worker <= 0 or num_gpus <= 0: return float('inf')

    # Distribute client indices among workers and assign GPUs round-robin
    all_client_indices = list(range(num_clients))
    tasks_per_worker = num_clients // num_workers
    worker_assignments = []
    start_idx = 0
    for i in range(num_workers):
        end_idx = start_idx + tasks_per_worker + (1 if i < num_clients % num_workers else 0)
        assigned_indices = all_client_indices[start_idx:end_idx]
        if assigned_indices:
            assigned_gpu_idx = gpu_ids[i % num_gpus]
            assigned_device_str = f"cuda:{assigned_gpu_idx}"
            worker_assignments.append((i, assigned_indices, assigned_device_str, model_params, train_params, num_streams_per_worker))
        start_idx = end_idx

    if not worker_assignments: return float('inf')

    # Sync all target GPUs before starting the main timer
    for gpu_id in gpu_ids:
         torch.cuda.synchronize(device=f"cuda:{gpu_id}")

    start_time = time.perf_counter() # Timer starts before pool creation/task submission
    with mp.Pool(processes=num_workers) as pool:
        results = pool.starmap(worker_train_multi_stream, worker_assignments)
    end_time = time.perf_counter() # Timer ends after all workers finished

    total_time = end_time - start_time
    print(f"总时间: {total_time:.6f} 秒")
    return total_time


# --- Main Execution Logic ---
def main(args):
    try:
        mp.set_start_method('spawn', force=True)
        print("使用 'spawn' 启动方法.")
    except RuntimeError:
        print("无法设置 'spawn' 启动方法 (可能已设置或不支持).")

    # --- GPU Handling ---
    if not args.gpus:
        print("错误：必须通过 --gpus 提供至少一个GPU ID。例如 --gpus 0 或 --gpus 0 1")
        return
    gpu_ids = args.gpus
    available_gpus = torch.cuda.device_count()
    for gpu_id in gpu_ids:
        if gpu_id < 0 or gpu_id >= available_gpus:
            print(f"错误：GPU ID {gpu_id} 无效。可用的 GPUs: {list(range(available_gpus))}")
            return
    print(f"将使用 GPUs: {gpu_ids}")
    sequential_device = f"cuda:{gpu_ids[0]}" # Sequential baseline runs on the first GPU

    print(f"\n测试参数:")
    # ... (rest of the parameter printing)
    print(f"  GPU IDs: {gpu_ids}")
    print(f"  模拟客户端数: {args.num_clients}")
    print(f"  本地 Epochs: {args.local_epochs}")
    print(f"  每 Epoch Batch数: {args.batches_per_epoch}")
    print(f"  Batch Size: {args.batch_size}")
    print(f"  输入维度: {args.input_dim}")
    print(f"  隐藏维度: {args.hidden_dim}")
    print(f"  进程数 (Workers): {args.num_workers}")
    print(f"  每个 Worker 的流数 (场景3): {args.num_streams_per_worker}")
    print(f"  运行次数: {args.runs}")


    # Auto-adjust num_workers
    if args.num_clients < args.num_workers:
         print(f"警告: 客户端数量 ({args.num_clients}) 少于 Worker 数量 ({args.num_workers}). 将使用 {args.num_clients} 个 Workers.")
         args.num_workers = args.num_clients
    elif args.num_workers <= 0:
        cpu_cores = os.cpu_count()
        suggested_workers = max(1, cpu_cores if cpu_cores else 1)
        args.num_workers = max(1, min(args.num_clients, suggested_workers))

    # Package parameters
    model_params = { 'input_dim': args.input_dim, 'hidden_dim': args.hidden_dim, 'output_dim': 10 }
    train_params = { 'local_epochs': args.local_epochs, 'batches_per_epoch': args.batches_per_epoch, 'batch_size': args.batch_size, }

    results = {'seq': [], 'mp_default': [], 'mp_multi': []}

    for r in range(args.runs + 1): # +1 for warmup run
        is_warmup = (r == 0)
        print(f"--- 第 {r if not is_warmup else '预热'} 次运行 ---")

        # Scenario 1 (on first specified GPU)
        t_seq = run_sequential_training(args.num_clients, sequential_device, model_params, train_params)
        if not is_warmup: results['seq'].append(t_seq)

        # Scenario 2 (distributed across specified GPUs)
        t_mp_default = run_mp_default_stream_training(args.num_clients, gpu_ids, model_params, train_params, args.num_workers)
        if not is_warmup: results['mp_default'].append(t_mp_default)

        # Scenario 3 (distributed across specified GPUs)
        t_mp_multi = run_mp_multi_stream_training(args.num_clients, gpu_ids, model_params, train_params, args.num_workers, args.num_streams_per_worker)
        if not is_warmup: results['mp_multi'].append(t_mp_multi)

        print("-" * (20 if is_warmup else 40) )

    # Calculate average times and print results (same as before)
    # ... [rest of the result calculation and printing code remains the same] ...
    avg_seq = sum(results['seq']) / args.runs if results['seq'] else float('inf')
    avg_mp_default = sum(results['mp_default']) / args.runs if results['mp_default'] else float('inf')
    avg_mp_multi = sum(results['mp_multi']) / args.runs if results['mp_multi'] else float('inf')

    print("\n--- 平均结果 ---")
    print(f"场景 1 (顺序, GPU {gpu_ids[0]}):             {avg_seq:.6f} 秒")
    print(f"场景 2 (MP 默认流, GPUs {gpu_ids}):        {avg_mp_default:.6f} 秒")
    print(f"场景 3 (MP {args.num_streams_per_worker}流/Worker, GPUs {gpu_ids}): {avg_mp_multi:.6f} 秒")

    # Conclusion (same logic as before)
    print("\n--- 结论 ---")
    times = {'顺序': avg_seq, 'MP 默认流': avg_mp_default, f'MP {args.num_streams_per_worker}流': avg_mp_multi}
    valid_times = {k: v for k, v in times.items() if v != float('inf')}

    if not valid_times:
        print("所有测试均未成功运行。")
    else:
        best_scenario = min(valid_times, key=valid_times.get)
        best_time = valid_times[best_scenario]
        print(f"最佳: {best_scenario} ({best_time:.6f} 秒)")

        if best_scenario != '顺序' and avg_seq != float('inf'):
            print(f"  - 比 顺序 (单GPU) 快 {avg_seq / best_time:.2f} 倍")

        if best_scenario == f'MP {args.num_streams_per_worker}流' and 'MP 默认流' in valid_times and valid_times['MP 默认流'] != float('inf'):
            if best_time < valid_times['MP 默认流']:
                print(f"  - 比 MP 默认流 快 {valid_times['MP 默认流'] / best_time:.2f} 倍")
            elif best_time > valid_times['MP 默认流']:
                 print(f"  - 比 MP 默认流 慢 {best_time / valid_times['MP 默认流']:.2f} 倍")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='比较模拟FL客户端训练的并行策略性能 (Multi-GPU & Refined Timing)')
    # --- Multi-GPU Argument ---
    parser.add_argument('--gpus', type=int, nargs='+', required=True, help='要使用的GPU ID列表 (例如: 0 或 0 1)')
    # --- Other Arguments (same as before) ---
    parser.add_argument('--num_clients', type=int, default=40, help='模拟的客户端总数')
    parser.add_argument('--local_epochs', type=int, default=2, help='每个客户端本地训练的Epochs数')
    parser.add_argument('--batches_per_epoch', type=int, default=10, help='每个Epoch的Batch数')
    parser.add_argument('--batch_size', type=int, default=64, help='Batch大小')
    parser.add_argument('--input_dim', type=int, default=784, help='模型输入维度')
    parser.add_argument('--hidden_dim', type=int, default=128, help='模型隐藏层维度')
    parser.add_argument('--num_workers', type=int, default=8, help='多进程场景下的Worker数量 (建议 <= CPU核心数 * GPU数量)') # Adjusted suggestion
    parser.add_argument('--num_streams_per_worker', type=int, default=2, help='场景3中每个Worker使用的流数量 (建议使用较小值如1, 2, 4)') # Adjusted default and suggestion
    parser.add_argument('--runs', type=int, default=3, help='测试运行次数 (不含预热)')

    args = parser.parse_args()

    # Auto-adjust num_workers suggestion (more aggressive with multi-gpu)
    cpu_cores = os.cpu_count()
    suggested_workers = max(1, cpu_cores * len(args.gpus) if cpu_cores else len(args.gpus)) # Can potentially use more workers now
    if args.num_clients < args.num_workers:
         print(f"警告: 客户端数量 ({args.num_clients}) 少于 Worker 数量 ({args.num_workers}). 将使用 {args.num_clients} 个 Workers.")
         args.num_workers = args.num_clients
    elif args.num_workers <= 0:
        args.num_workers = max(1, min(args.num_clients, suggested_workers))
        print(f"自动设置 num_workers 为 {args.num_workers}")


    main(args)