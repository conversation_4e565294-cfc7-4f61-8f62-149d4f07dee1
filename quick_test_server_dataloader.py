#!/usr/bin/env python3
"""
Quick test script to verify server dataloader functionality.
This specifically tests the client_idx=None case used in nodes.py.
"""

import sys
sys.path.append('/home/<USER>/federated-learning/fl-sim')

from fl_sim.data_processing.fed_cifar import FedCIFAR100_LDA
import numpy as np
from collections import Counter

def test_server_dataloader():
    """Test server dataloader functionality (client_idx=None)."""
    
    print("=== Quick Server Dataloader Test ===\n")
    
    # Initialize dataset
    print("1. Initializing FedCIFAR100_LDA...")
    dataset = FedCIFAR100_LDA(
        lda_alpha=0.1,
        num_clients=10,  # Small number for quick test
        transform="none",
        seed=42
    )
    print(f"   ✓ Dataset initialized with {dataset.num_clients} clients")
    
    # Test server dataloader (client_idx=None)
    print("\n2. Testing server dataloader (client_idx=None)...")
    try:
        server_train_dl, server_test_dl = dataset.get_dataloader(
            train_bs=128,
            test_bs=128,
            client_idx=None  # This is what nodes.py line 599 uses
        )
        print(f"   ✓ Server dataloader created successfully")
        print(f"   - Server training samples: {len(server_train_dl.dataset)}")
        print(f"   - Server test samples: {len(server_test_dl.dataset)}")
        
        # Verify test data completeness
        expected_test_samples = dataset.DEFAULT_TEST_CLIENTS_NUM * 100  # 100 clients * ~100 samples each
        if len(server_test_dl.dataset) == expected_test_samples:
            print(f"   ✓ Server test data contains all {expected_test_samples} original test samples")
        else:
            print(f"   ⚠ Expected {expected_test_samples}, got {len(server_test_dl.dataset)}")
        
        # Test data loading
        print("\n3. Testing data loading...")
        train_batch = next(iter(server_train_dl))
        test_batch = next(iter(server_test_dl))
        
        print(f"   ✓ Training batch shape: {train_batch[0].shape}, {train_batch[1].shape}")
        print(f"   ✓ Test batch shape: {test_batch[0].shape}, {test_batch[1].shape}")
        
        # Verify all classes present
        print("\n4. Verifying class coverage...")
        test_labels = []
        for batch_x, batch_y in server_test_dl:
            test_labels.extend(batch_y.numpy().tolist())
        
        unique_classes = len(set(test_labels))
        print(f"   ✓ Server test data contains {unique_classes}/{dataset.n_class} classes")
        
        if unique_classes == dataset.n_class:
            print(f"   ✓ All classes present - perfect for server evaluation!")
        else:
            print(f"   ⚠ Missing {dataset.n_class - unique_classes} classes")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Server dataloader creation failed: {e}")
        return False

def test_client_vs_server():
    """Compare client-specific vs server dataloaders."""
    
    print("\n=== Client vs Server Comparison ===\n")
    
    dataset = FedCIFAR100_LDA(
        lda_alpha=0.1,
        num_clients=5,
        transform="none",
        seed=42
    )
    
    # Get server dataloader
    _, server_test_dl = dataset.get_dataloader(client_idx=None)
    
    # Get client dataloaders
    client_test_samples = []
    for client_idx in range(dataset.num_clients):
        _, client_test_dl = dataset.get_dataloader(client_idx=client_idx)
        client_test_samples.append(len(client_test_dl.dataset))
    
    print(f"Server test samples: {len(server_test_dl.dataset)}")
    print(f"Client test samples: {client_test_samples}")
    print(f"Total client test samples: {sum(client_test_samples)}")
    print(f"Average per client: {np.mean(client_test_samples):.1f}")
    
    # This demonstrates that:
    # - Server gets ALL test data (for comprehensive evaluation)
    # - Each client gets only a subset (memory efficient)
    print(f"\n✓ Server has complete test set for evaluation")
    print(f"✓ Clients have distributed test subsets")
    print(f"✓ Ready for federated learning scenarios")

if __name__ == "__main__":
    success1 = test_server_dataloader()
    test_client_vs_server()
    
    if success1:
        print(f"\n🎉 All tests passed!")
        print(f"✅ Ready for use in nodes.py line 599")
        print(f"✅ Compatible with server evaluation (lines 990-1000)")
    else:
        print(f"\n❌ Some tests failed!")
    
    print(f"\n" + "="*50)
    print(f"NODES.PY INTEGRATION READY")
    print(f"="*50)
